import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { Search, Eye, Edit, Trash2, UserPlus, Users, Activity, Mail, Phone, Building, FileText, Plus, Download, Upload } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { apiRequest } from '@/lib/queryClient';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  Di<PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Mandant {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  siret: string;
  address: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
  contracts: {
    total: number;
    active: number;
    completed: number;
  };
}

interface MandantFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  siret: string;
  address: string;
}

export function AdminMandantsPage() {
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { isAdmin } = useAdmin();

  const [mandants, setMandants] = useState<Mandant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    newThisMonth: 0,
    totalContracts: 0
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedMandant, setSelectedMandant] = useState<Mandant | null>(null);
  const [formData, setFormData] = useState<MandantFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    siret: '',
    address: ''
  });

  useEffect(() => {
    if (!isAuthenticated) {
      setLocation('/');
      return;
    }

    if (!isAdmin) {
      setLocation('/contracts');
      return;
    }

    loadMandants();
  }, [isAuthenticated, isAdmin, setLocation]);

  const loadMandants = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest('GET', '/api/admin/mandants');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setMandants(data.mandants);
      setStats(data.stats);
    } catch (error) {
      console.error('Erreur lors du chargement des mandants:', error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors du chargement des mandants. Veuillez réessayer.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateMandant = async () => {
    try {
      const response = await apiRequest('POST', '/api/admin/mandants', formData);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      toast({
        title: "Succès",
        description: "Le mandant a été créé avec succès.",
      });

      setIsDialogOpen(false);
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        siret: '',
        address: ''
      });
      loadMandants();
    } catch (error) {
      console.error('Erreur lors de la création du mandant:', error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du mandant.",
        variant: "destructive",
      });
    }
  };

  const handleUpdateMandant = async () => {
    if (!selectedMandant) return;

    try {
      const response = await apiRequest('PUT', `/api/admin/mandants/${selectedMandant.id}`, formData);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      toast({
        title: "Succès",
        description: "Le mandant a été mis à jour avec succès.",
      });

      setIsDialogOpen(false);
      setSelectedMandant(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        siret: '',
        address: ''
      });
      loadMandants();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du mandant:', error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour du mandant.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteMandant = async (mandantId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce mandant ? Cette action est irréversible.')) {
      try {
        const response = await apiRequest('DELETE', `/api/admin/mandants/${mandantId}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        toast({
          title: "Succès",
          description: "Le mandant a été supprimé avec succès.",
        });

        loadMandants();
      } catch (error) {
        console.error('Erreur lors de la suppression du mandant:', error);
        toast({
          title: "Erreur",
          description: "Une erreur est survenue lors de la suppression du mandant.",
          variant: "destructive",
        });
      }
    }
  };

  const handleExportMandants = () => {
    const csvContent = [
      ['Nom', 'Email', 'Téléphone', 'Entreprise', 'SIRET', 'Adresse', 'Date de création', 'Statut'],
      ...mandants.map(mandant => [
        mandant.name,
        mandant.email,
        mandant.phone,
        mandant.company,
        mandant.siret,
        mandant.address,
        formatDate(mandant.created_at),
        mandant.is_active ? 'Actif' : 'Inactif'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `mandants_${formatDate(new Date())}.csv`;
    link.click();
  };

  const filteredMandants = mandants.filter(mandant => {
    const matchesSearch =
      mandant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mandant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mandant.company.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'active') return matchesSearch && mandant.is_active;
    if (activeTab === 'inactive') return matchesSearch && !mandant.is_active;
    return matchesSearch;
  });

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Gestion des Mandants</h1>
          <div className="flex gap-2">
            <Button onClick={handleExportMandants} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Exporter
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => {
                  setSelectedMandant(null);
                  setFormData({
                    name: '',
                    email: '',
                    phone: '',
                    company: '',
                    siret: '',
                    address: ''
                  });
                }}>
                  <Plus className="mr-2 h-4 w-4" />
                  Nouveau Mandant
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{selectedMandant ? 'Modifier le mandant' : 'Nouveau mandant'}</DialogTitle>
                  <DialogDescription>
                    {selectedMandant ? 'Modifiez les informations du mandant.' : 'Remplissez les informations pour créer un nouveau mandant.'}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="name" className="text-right">Nom</label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="email" className="text-right">Email</label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="phone" className="text-right">Téléphone</label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="company" className="text-right">Entreprise</label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="siret" className="text-right">SIRET</label>
                    <Input
                      id="siret"
                      value={formData.siret}
                      onChange={(e) => setFormData({ ...formData, siret: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="address" className="text-right">Adresse</label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>Annuler</Button>
                  <Button onClick={selectedMandant ? handleUpdateMandant : handleCreateMandant}>
                    {selectedMandant ? 'Mettre à jour' : 'Créer'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Mandants</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Mandants Actifs</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.active}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Nouveaux ce mois</CardTitle>
              <UserPlus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.newThisMonth}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Contrats</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalContracts}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filtres et recherche */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Rechercher un mandant..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
              <TabsList>
                <TabsTrigger value="all">Tous</TabsTrigger>
                <TabsTrigger value="active">Actifs</TabsTrigger>
                <TabsTrigger value="inactive">Inactifs</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Liste des mandants */}
        {filteredMandants.length === 0 ? (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun mandant</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Commencez par créer un nouveau mandant.
            </p>
            <div className="mt-6">
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Nouveau Mandant
              </Button>
            </div>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Entreprise</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Contrats</TableHead>
                  <TableHead>Dernière connexion</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMandants.map((mandant) => (
                  <TableRow key={mandant.id}>
                    <TableCell className="font-medium">{mandant.name}</TableCell>
                    <TableCell>{mandant.email}</TableCell>
                    <TableCell>{mandant.company}</TableCell>
                    <TableCell>
                      <Badge variant={mandant.is_active ? "success" : "secondary"}>
                        {mandant.is_active ? "Actif" : "Inactif"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{mandant.contracts.total}</span>
                        <Badge variant="outline" className="text-xs">
                          {mandant.contracts.active} actifs
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {mandant.last_login ? formatDate(mandant.last_login) : 'Jamais'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Ouvrir le menu</span>
                            <svg
                              className="h-4 w-4"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="1" />
                              <circle cx="12" cy="5" r="1" />
                              <circle cx="12" cy="19" r="1" />
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => setLocation(`/mandant/${mandant.id}`)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Voir
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedMandant(mandant);
                              setFormData({
                                name: mandant.name,
                                email: mandant.email,
                                phone: mandant.phone,
                                company: mandant.company,
                                siret: mandant.siret,
                                address: mandant.address
                              });
                              setIsDialogOpen(true);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Modifier
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteMandant(mandant.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Supprimer
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
}