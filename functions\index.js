const functions = require("firebase-functions");
const admin = require("firebase-admin");
admin.initializeApp();

exports.setAdminClaim = functions.https.onCall(async (data, context) => {
  const { email, password } = data;
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "Utilisateur non authentifié.");
  }

  if (email !== "<EMAIL>" || password !== "Akissi6851@") {
    throw new functions.https.HttpsError("permission-denied", "Identifiants admin invalides.");
  }

  try {
    await admin.auth().setCustomUserClaims(context.auth.uid, { admin: true });
    return { success: true };
  } catch (error) {
    throw new functions.https.HttpsError("internal", "Échec de la définition des claims admin.");
  }
});