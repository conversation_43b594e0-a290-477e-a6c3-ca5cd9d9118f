import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { Search, Eye, User, Calendar, ArrowUpDown, Edit, Trash2, Building2 } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { apiRequest } from '@/lib/queryClient';

// Types pour les mandants (créanciers)
interface Mandant {
  id: string;
  name: string;
  email: string;
  company: string;
  phone: string;
  address: string;
  createdAt: string;
  contracts: number;
  status: 'active' | 'inactive';
}

export function MandantsListPage() {
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { isAdmin } = useAdmin();

  const [mandants, setMandants] = useState<Mandant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Mandant>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Fonction pour charger les mandants
  const loadMandants = async () => {
    setIsLoading(true);
    try {
      console.log('Chargement des mandants...');
      const response = await apiRequest('GET', '/api/mandants');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Mandants chargés:', data);
      setMandants(data.mandants);
    } catch (error) {
      console.error('Erreur lors du chargement des mandants:', error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors du chargement des mandants. Veuillez réessayer.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour gérer la suppression d'un mandant
  const handleDeleteMandant = async (mandantId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce mandant ? Cette action est irréversible.')) {
      try {
        await apiRequest('DELETE', `/api/mandants/${mandantId}`);
        toast({
          title: "Succès",
          description: "Le mandant a été supprimé avec succès.",
        });
        // Recharger la liste des mandants
        loadMandants();
      } catch (error) {
        console.error('Erreur lors de la suppression du mandant:', error);
        toast({
          title: "Erreur",
          description: "Une erreur est survenue lors de la suppression du mandant.",
          variant: "destructive",
        });
      }
    }
  };

  // Vérification de l'authentification et du rôle admin (temporairement désactivée)
  useEffect(() => {
    console.log('Vérification auth - isAuthenticated:', isAuthenticated, 'isAdmin:', isAdmin);

    if (!authLoading) {
      // Temporairement désactivé pour le mode développement
      // if (!isAuthenticated) {
      //   console.log('Non authentifié, redirection vers /');
      //   setLocation('/');
      // } else if (!isAdmin) {
      //   console.log('Non admin, redirection vers /contracts');
      //   setLocation('/contracts');
      // } else {
        console.log('Chargement des mandants (mode dev)');
        loadMandants();
      // }
    }
  }, [isAuthenticated, isAdmin, authLoading, setLocation]);

  // Fonction pour trier les mandants
  const handleSort = (field: keyof Mandant) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Filtrer et trier les mandants
  const filteredAndSortedMandants = mandants
    .filter(mandant =>
      mandant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mandant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mandant.company.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const fieldA = a[sortField];
      const fieldB = b[sortField];

      if (typeof fieldA === 'string' && typeof fieldB === 'string') {
        return sortDirection === 'asc'
          ? fieldA.localeCompare(fieldB)
          : fieldB.localeCompare(fieldA);
      }

      if (typeof fieldA === 'number' && typeof fieldB === 'number') {
        return sortDirection === 'asc'
          ? fieldA - fieldB
          : fieldB - fieldA;
      }

      return 0;
    });

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Temporairement désactivé pour le mode développement
  // if (!isAuthenticated || !isAdmin) {
  //   return null;
  // }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Liste des Mandants</h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Gérez vos créanciers et leurs contrats
            </p>
          </div>

          <div className="flex space-x-4">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Rechercher un mandant..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full"
              />
            </div>
            <Button
              onClick={() => setLocation('/mandant/new')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <User className="h-4 w-4 mr-2" />
              Nouveau Mandant
            </Button>
          </div>
        </div>

        {filteredAndSortedMandants.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Aucun mandant trouvé</h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm
                ? `Aucun résultat pour "${searchTerm}"`
                : "Vous n'avez pas encore de mandants enregistrés."}
            </p>
            <Button
              onClick={() => setLocation('/mandant/new')}
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <User className="h-4 w-4 mr-2" />
              Ajouter un mandant
            </Button>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center">
                      Nom
                      {sortField === 'name' && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('company')}
                  >
                    <div className="flex items-center">
                      Entreprise
                      {sortField === 'company' && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('email')}
                  >
                    <div className="flex items-center">
                      Email
                      {sortField === 'email' && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('createdAt')}
                  >
                    <div className="flex items-center">
                      Date d'inscription
                      {sortField === 'createdAt' && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('contracts')}
                  >
                    <div className="flex items-center">
                      Contrats
                      {sortField === 'contracts' && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredAndSortedMandants.map((mandant) => (
                  <tr key={mandant.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {mandant.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {mandant.company}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {mandant.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-gray-400" />
                        {formatDate(mandant.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200">
                        {mandant.contracts}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setLocation(`/mandant/${mandant.id}`)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Voir
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setLocation(`/mandant/${mandant.id}/edit`)}
                          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Modifier
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteMandant(mandant.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Supprimer
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
