import { useQuery } from '@tanstack/react-query';
import { Contract } from '@/types';
import { apiRequest } from '@/lib/queryClient';

type ApiError = {
  message: string;
  status?: number;
  details?: unknown;
};

export function useContract(contractId?: string) {
  const contractQuery = useQuery<Contract, ApiError>({
    queryKey: ['contract', contractId],
    queryFn: async () => {
      if (!contractId) {
        throw new Error('ID du contrat non spécifié');
      }
      const response = await apiRequest('GET', `/api/contracts/${contractId}`);
      return await response.json();
    },
    enabled: !!contractId,
  });

  return {
    contract: contractQuery.data,
    isLoading: contractQuery.isLoading,
    error: contractQuery.error,
  };
} 