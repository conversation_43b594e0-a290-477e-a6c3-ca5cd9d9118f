import { useState, useEffect, useCallback } from 'react';

export function useTheme() {
  const [isDark, setIsDark] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Fonction pour appliquer le thème avec transition
  const updateTheme = useCallback((dark: boolean) => {
    // Marquer le début de la transition
    setIsTransitioning(true);

    // Appliquer la classe dark au document
    if (dark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Sauvegarder la préférence dans le localStorage
    localStorage.setItem('theme', dark ? 'dark' : 'light');

    // Réinitialiser l'état de transition après la durée de l'animation
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300); // Correspond à la durée de transition dans index.css
  }, []);

  // Initialiser le thème au chargement
  useEffect(() => {
    const stored = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = stored === 'dark' || (!stored && prefersDark);

    setIsDark(initialTheme);
    updateTheme(initialTheme);

    // Écouter les changements de préférence système
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      // Ne changer automatiquement que si l'utilisateur n'a pas défini de préférence
      if (!localStorage.getItem('theme')) {
        setIsDark(e.matches);
        updateTheme(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [updateTheme]);

  // Fonction pour basculer entre les thèmes
  const toggleTheme = useCallback(() => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    updateTheme(newTheme);
  }, [isDark, updateTheme]);

  return { isDark, isTransitioning, toggleTheme };
}