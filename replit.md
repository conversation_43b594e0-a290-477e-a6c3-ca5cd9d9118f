# RecovExpert - Debt Recovery Contract Management System

## Overview

RecovExpert is a full-stack web application for managing debt recovery contracts between creditors (mandataires) and debtors (mandants). The system provides a complete workflow for creating, sharing, signing, and managing debt recovery contracts with a multi-step process and real-time status tracking.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Framework**: Tailwind CSS with shadcn/ui components
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query for server state management
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database ORM**: Drizzle ORM with PostgreSQL
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Development**: In-memory storage fallback for development

### Monorepo Structure
- `client/` - React frontend application
- `server/` - Express.js backend API
- `shared/` - Shared TypeScript schemas and types

## Key Components

### Database Schema
- **Users Table**: User authentication and profile management
- **Contracts Table**: Contract lifecycle management with JSON fields for mandataire and mandant data
- **Status Tracking**: Contract states (en_attente, accepte, rejete, valide)

### Authentication System
- Simple email/password authentication
- Local storage for session persistence
- No external auth providers (ready for expansion)

### Contract Workflow
1. **Mandataire Form**: Creditor enters debt details and contact information
2. **Share Contract**: Generate shareable link for debtor
3. **Mandant Form**: Debtor enters their information and accepts/rejects
4. **Contract Articles**: Display legal contract terms
5. **Signature**: Final contract validation and signing

### API Endpoints
- `POST /api/auth/login` - User authentication
- `POST /api/auth/signup` - User registration
- `POST /api/contracts` - Create new contract
- `GET /api/contracts` - List user contracts
- `GET /api/contracts/:id` - Get specific contract
- `PUT /api/contracts/:id` - Update contract status/data

## Data Flow

### Contract Creation Flow
1. Authenticated user creates contract with mandataire data
2. System generates unique contract ID and shareable link
3. Link shared with debtor (mandant)
4. Debtor accesses contract via link, fills mandant form
5. Contract status updated through workflow states
6. Final contract generated with all parties' data

### Authentication Flow
1. User submits credentials to `/api/auth/login` or `/api/auth/signup`
2. Server validates and returns user object
3. Client stores user data in localStorage
4. Subsequent requests include user context

## External Dependencies

### UI Components
- **Radix UI**: Headless UI primitives for accessibility
- **Lucide React**: Icon library
- **React Icons**: Additional social media icons
- **Class Variance Authority**: Type-safe variant API for components

### Development Tools
- **ESBuild**: Fast JavaScript bundler for production
- **TSX**: TypeScript execution for development
- **PostCSS**: CSS processing with Tailwind

### Database & Validation
- **Zod**: Runtime type validation
- **Drizzle Kit**: Database migrations and introspection
- **Neon Database**: Serverless PostgreSQL hosting

## Deployment Strategy

### Development Environment
- Replit-hosted with live reload
- In-memory storage for rapid development
- Hot module replacement via Vite
- Development server on port 5000

### Production Build
- Frontend: Vite builds static assets to `dist/public`
- Backend: ESBuild bundles server to `dist/index.js`
- Database: Drizzle migrations via `npm run db:push`
- Deployment: Autoscale deployment target on Replit

### Environment Configuration
- `NODE_ENV` for development/production modes
- `DATABASE_URL` for PostgreSQL connection
- Graceful fallback to in-memory storage when database unavailable

### Replit Integration
- Cartographer plugin for development debugging
- Runtime error overlay for better DX
- Custom workflow for parallel development tasks