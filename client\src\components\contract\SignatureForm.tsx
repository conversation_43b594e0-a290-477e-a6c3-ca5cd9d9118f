import React, { useState, useRef, ChangeEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, ArrowRight, Upload, Pen, Image, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SignatureFormProps {
  onSubmit: (signatureData: { type: 'image' | 'text'; value: string }) => void;
  onBack: () => void;
}

export function SignatureForm({ onSubmit, onBack }: SignatureFormProps) {
  const [signatureType, setSignatureType] = useState<'image' | 'text'>('text');
  const [textSignature, setTextSignature] = useState('');
  const [imageSignature, setImageSignature] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      alert('Veuillez sélectionner une image valide (JPG, PNG, etc.)');
      setIsUploading(false);
      return;
    }

    // Vérifier la taille du fichier (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert('L\'image est trop volumineuse. Veuillez sélectionner une image de moins de 2MB.');
      setIsUploading(false);
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      setImageSignature(event.target?.result as string);
      setIsUploading(false);
    };
    reader.onerror = () => {
      alert('Erreur lors de la lecture du fichier. Veuillez réessayer.');
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (signatureType === 'text' && !textSignature.trim()) {
      alert('Veuillez entrer votre signature textuelle');
      return;
    }
    
    if (signatureType === 'image' && !imageSignature) {
      alert('Veuillez téléverser une image de signature');
      return;
    }
    
    onSubmit({
      type: signatureType,
      value: signatureType === 'text' ? textSignature : (imageSignature || '')
    });
  };

  const clearImageSignature = () => {
    setImageSignature(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Signature du Contrat</h2>
      
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="flex space-x-4 mb-6">
          <Button
            type="button"
            onClick={() => setSignatureType('text')}
            className={cn(
              "flex-1 py-6",
              signatureType === 'text'
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            <Pen className="mr-2 h-5 w-5" />
            Signature Textuelle
          </Button>
          
          <Button
            type="button"
            onClick={() => setSignatureType('image')}
            className={cn(
              "flex-1 py-6",
              signatureType === 'image'
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            <Image className="mr-2 h-5 w-5" />
            Téléverser une Signature
          </Button>
        </div>
        
        {signatureType === 'text' ? (
          <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
            <Label htmlFor="textSignature" className="block text-sm font-medium text-gray-700 mb-2">
              Votre signature textuelle (ex: "J'approuve")
            </Label>
            <Input
              id="textSignature"
              value={textSignature}
              onChange={(e) => setTextSignature(e.target.value)}
              placeholder="Tapez votre signature ici..."
              className="w-full"
              required
            />
            
            {textSignature && (
              <div className="mt-4 p-4 bg-white border border-gray-200 rounded-lg">
                <p className="text-sm text-gray-500 mb-2">Aperçu de la signature:</p>
                <p className="font-handwriting text-xl text-gray-800">{textSignature}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
            <Label htmlFor="imageSignature" className="block text-sm font-medium text-gray-700 mb-2">
              Téléverser une image de votre signature
            </Label>
            
            {!imageSignature ? (
              <div className="mt-2">
                <div className="flex items-center justify-center w-full">
                  <label
                    htmlFor="imageSignature"
                    className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-lg cursor-pointer bg-white hover:bg-gray-50"
                  >
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-10 h-10 mb-3 text-gray-400" />
                      <p className="mb-2 text-sm text-gray-500">
                        <span className="font-semibold">Cliquez pour téléverser</span> ou glissez-déposez
                      </p>
                      <p className="text-xs text-gray-500">PNG, JPG (MAX. 2MB)</p>
                    </div>
                    <Input
                      id="imageSignature"
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
            ) : (
              <div className="mt-4">
                <div className="relative">
                  <img
                    src={imageSignature}
                    alt="Signature"
                    className="max-h-40 max-w-full mx-auto border border-gray-200 rounded-lg"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={clearImageSignature}
                    className="absolute top-2 right-2"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
            
            {isUploading && (
              <div className="mt-4 flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">Chargement de l'image...</span>
              </div>
            )}
          </div>
        )}
        
        <div className="flex justify-between pt-4">
          <Button type="button" variant="ghost" onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
          <Button 
            type="submit" 
            className="bg-primary text-white hover:bg-blue-700 px-8 py-3"
            disabled={
              (signatureType === 'text' && !textSignature.trim()) || 
              (signatureType === 'image' && !imageSignature) ||
              isUploading
            }
          >
            Valider et finaliser
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}
