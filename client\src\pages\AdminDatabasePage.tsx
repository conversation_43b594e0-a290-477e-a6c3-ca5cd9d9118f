import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { Navigation } from '@/components/Navigation';

interface DatabaseStatus {
  publicUsers: number;
  contracts: number;
  loginAttempts: number;
  authUsers: number;
  authUsersList: Array<{
    id: string;
    email: string;
    created_at: string;
  }>;
}

interface RepairResult {
  message: string;
  repairedUsers: string[];
  skippedUsers: string[];
  errors: string[];
  totalAuthUsers: number;
  repairedCount: number;
  skippedCount: number;
}

export function AdminDatabasePage() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [repairResult, setRepairResult] = useState<RepairResult | null>(null);

  const checkDatabaseStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/database-status');
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
        toast({
          title: "État vérifié",
          description: "L'état de la base de données a été récupéré avec succès.",
        });
      } else {
        throw new Error('Erreur lors de la vérification');
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de vérifier l'état de la base de données.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const repairUsers = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/repair-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (response.ok) {
        const data = await response.json();
        setRepairResult(data);
        toast({
          title: "Réparation terminée",
          description: `${data.repairedCount} utilisateur(s) réparé(s) avec succès.`,
        });
        // Rafraîchir le statut
        await checkDatabaseStatus();
      } else {
        throw new Error('Erreur lors de la réparation');
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de réparer les utilisateurs.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            🛠️ Administration - Base de données
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Outils pour diagnostiquer et réparer les problèmes de base de données
          </p>
        </div>

        {/* État de la base de données */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>📊 État de la base de données</CardTitle>
            <CardDescription>
              Vérifiez l'état actuel des tables et des utilisateurs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={checkDatabaseStatus}
              disabled={isLoading}
              className="mb-4"
            >
              {isLoading ? 'Vérification...' : 'Vérifier l\'état'}
            </Button>

            {status && (
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Statistiques :</h3>
                <ul className="space-y-1 text-sm">
                  <li>• Utilisateurs (public.users) : {status.publicUsers}</li>
                  <li>• Contrats : {status.contracts}</li>
                  <li>• Tentatives de connexion : {status.loginAttempts}</li>
                  <li>• Utilisateurs Auth : {status.authUsers}</li>
                </ul>

                {status.authUsersList.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Utilisateurs Auth détaillés :</h4>
                    <div className="max-h-40 overflow-y-auto">
                      {status.authUsersList.map((user) => (
                        <div key={user.id} className="text-xs bg-white dark:bg-gray-700 p-2 mb-1 rounded">
                          <div><strong>{user.email}</strong></div>
                          <div className="text-gray-500">ID: {user.id}</div>
                          <div className="text-gray-500">Créé: {new Date(user.created_at).toLocaleString()}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Réparation des utilisateurs */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>🔧 Réparer les utilisateurs orphelins</CardTitle>
            <CardDescription>
              Répare les utilisateurs qui existent dans auth.users mais pas dans public.users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={repairUsers}
              disabled={isLoading}
              className="mb-4"
            >
              {isLoading ? 'Réparation...' : 'Réparer les utilisateurs'}
            </Button>

            {repairResult && (
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h3 className="font-semibold mb-2 text-green-800 dark:text-green-200">
                  ✅ {repairResult.message}
                </h3>
                <div className="text-sm space-y-1">
                  <p>• Utilisateurs auth trouvés : {repairResult.totalAuthUsers}</p>
                  <p>• Utilisateurs réparés : {repairResult.repairedCount}</p>
                  <p>• Utilisateurs ignorés (déjà OK) : {repairResult.skippedCount}</p>
                </div>

                {repairResult.repairedUsers.length > 0 && (
                  <div className="mt-2">
                    <p className="font-medium">Utilisateurs réparés :</p>
                    <ul className="text-sm">
                      {repairResult.repairedUsers.map((email, index) => (
                        <li key={index}>• {email}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {repairResult.skippedUsers.length > 0 && (
                  <div className="mt-2">
                    <p className="font-medium">Utilisateurs déjà OK :</p>
                    <ul className="text-sm text-gray-600">
                      {repairResult.skippedUsers.map((email, index) => (
                        <li key={index}>• {email}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {repairResult.errors.length > 0 && (
                  <div className="mt-2">
                    <p className="font-medium text-red-600">Erreurs :</p>
                    <ul className="text-sm text-red-600">
                      {repairResult.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                  <p className="text-blue-800 dark:text-blue-200 font-medium">
                    🎉 Vous pouvez maintenant essayer de vous connecter !
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>1.</strong> Vérifiez d'abord l'état de la base de données</p>
              <p><strong>2.</strong> Si vous voyez des utilisateurs dans "Auth" mais 0 dans "public.users", cliquez sur "Réparer"</p>
              <p><strong>3.</strong> Après la réparation, testez la connexion sur la page principale</p>
              <p><strong>4.</strong> L'erreur "Ce compte a été désactivé" devrait disparaître</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
