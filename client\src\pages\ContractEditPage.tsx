import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useParams } from 'wouter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useContract } from '@/hooks/useContract';
import { Contract, MandataireData, MandantData } from '@/types';
import { toast } from '@/components/ui/use-toast';
import { ArrowLeft } from 'lucide-react';

export function ContractEditPage() {
  const [, setLocation] = useLocation();
  const [, params] = useParams();
  const contractId = params?.id;
  const isNewContract = !contractId;
  const { contract, isLoading, error } = useContract(contractId);

  const [formData, setFormData] = useState<Partial<Contract>>({
    status: 'en_attente',
    mandataireData: {
      nom: '',
      email: '',
      telephone: '',
      adresse: '',
      creance: '',
      montant: 0,
      commission: 0
    },
    mandantData: {
      nom: '',
      email: '',
      telephone: '',
      adresse: ''
    }
  });

  useEffect(() => {
    if (contract) {
      setFormData(contract);
    }
  }, [contract]);

  const handleChange = (section: 'mandataireData' | 'mandantData', field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const url = isNewContract ? '/api/contracts' : `/api/contracts/${contractId}`;
      const method = isNewContract ? 'POST' : 'PUT';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la sauvegarde du contrat');
      }

      toast({
        title: isNewContract ? "Contrat créé" : "Contrat mis à jour",
        description: isNewContract 
          ? "Le contrat a été créé avec succès."
          : "Le contrat a été mis à jour avec succès.",
      });

      setLocation('/contracts');
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la sauvegarde du contrat.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  if (error && !isNewContract) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Erreur lors du chargement du contrat
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error.message || "Le contrat n'a pas été trouvé"}
          </p>
          <Button onClick={() => setLocation('/contracts')}>
            Retour à la liste des contrats
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setLocation('/contracts')}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          {isNewContract ? 'Nouveau Contrat' : 'Modifier le Contrat'}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Informations du Mandataire</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="mandataire-nom">Nom</Label>
              <Input
                id="mandataire-nom"
                value={formData.mandataireData?.nom || ''}
                onChange={(e) => handleChange('mandataireData', 'nom', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandataire-email">Email</Label>
              <Input
                id="mandataire-email"
                type="email"
                value={formData.mandataireData?.email || ''}
                onChange={(e) => handleChange('mandataireData', 'email', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandataire-telephone">Téléphone</Label>
              <Input
                id="mandataire-telephone"
                type="tel"
                value={formData.mandataireData?.telephone || ''}
                onChange={(e) => handleChange('mandataireData', 'telephone', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandataire-adresse">Adresse</Label>
              <Input
                id="mandataire-adresse"
                value={formData.mandataireData?.adresse || ''}
                onChange={(e) => handleChange('mandataireData', 'adresse', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandataire-creance">Créance</Label>
              <Input
                id="mandataire-creance"
                value={formData.mandataireData?.creance || ''}
                onChange={(e) => handleChange('mandataireData', 'creance', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandataire-montant">Montant (€)</Label>
              <Input
                id="mandataire-montant"
                type="number"
                value={formData.mandataireData?.montant || 0}
                onChange={(e) => handleChange('mandataireData', 'montant', parseFloat(e.target.value))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandataire-commission">Commission (%)</Label>
              <Input
                id="mandataire-commission"
                type="number"
                value={formData.mandataireData?.commission || 0}
                onChange={(e) => handleChange('mandataireData', 'commission', parseFloat(e.target.value))}
                required
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informations du Mandant</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="mandant-nom">Nom</Label>
              <Input
                id="mandant-nom"
                value={formData.mandantData?.nom || ''}
                onChange={(e) => handleChange('mandantData', 'nom', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandant-email">Email</Label>
              <Input
                id="mandant-email"
                type="email"
                value={formData.mandantData?.email || ''}
                onChange={(e) => handleChange('mandantData', 'email', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandant-telephone">Téléphone</Label>
              <Input
                id="mandant-telephone"
                type="tel"
                value={formData.mandantData?.telephone || ''}
                onChange={(e) => handleChange('mandantData', 'telephone', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mandant-adresse">Adresse</Label>
              <Input
                id="mandant-adresse"
                value={formData.mandantData?.adresse || ''}
                onChange={(e) => handleChange('mandantData', 'adresse', e.target.value)}
                required
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setLocation('/contracts')}
          >
            Annuler
          </Button>
          <Button type="submit">
            {isNewContract ? 'Créer' : 'Enregistrer'}
          </Button>
        </div>
      </form>
    </div>
  );
} 