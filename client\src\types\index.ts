export interface User {
  getIdTokenResult(): unknown;
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  neighborhood: string;
  profession: string;
  role: 'admin' | 'mandant';
  createdAt: string;
  lastLogin?: string;
  isActive: boolean;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface MandataireData {
  nom: string;
  email: string;
  telephone: string;
  siret?: string;
  adresse: string;
  creance: string;
  montant: number;
  commission: number;
}

export interface MandantData {
  nom: string;
  email: string;
  telephone: string;
  siret?: string;
  adresse: string;
  formeJuridique?: string;
  representant?: string;
  fonction?: string;
  banque?: string;
  numeroCompte?: string;
  iban?: string;
  swift?: string;
}

export interface Contract {
  id: string;
  status: 'en_attente' | 'accepte' | 'rejete' | 'valide';
  createdAt: string;
  signedAt?: string | null;
  mandataireData?: MandataireData | null;
  mandantData?: MandantData | null;
  userId?: number | null;
  mandantId?: string | null;
  signature?: {
    type: 'image' | 'text';
    value: string;
  } | null;
}

export interface ContractArticle {
  title: string;
  content: string;
}
