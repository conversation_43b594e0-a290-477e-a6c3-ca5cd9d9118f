import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types pour les tables Supabase
export type User = {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string | null;
  country: string | null;
  city: string | null;
  neighborhood: string | null;
  profession: string | null;
  role: 'admin' | 'mandant';
  is_active: boolean;
  created_at: string;
  last_login: string | null;
  updated_at: string;
};

export type Contract = {
  id: string;
  userId: string;
  status: 'en_attente' | 'en_cours' | 'termine' | 'annule';
  mandataireData: {
    name: string;
    phone: string;
    email: string;
    address: string;
  };
  mandantData: {
    name: string;
    phone: string;
    email: string;
    address: string;
  } | null;
  createdAt: string;
  updatedAt: string;
};

export type LoginAttempt = {
  id: string;
  user_id: string | null;
  email: string;
  success: boolean;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
};

// Fonctions utilitaires
export const logLoginAttempt = async (
  userId: string | null,
  email: string,
  success: boolean,
  userAgent: string | null
) => {
  const { error } = await supabase.from('login_attempts').insert({
    user_id: userId,
    email,
    success,
    user_agent: userAgent,
    ip_address: null, // Vous pouvez ajouter la détection d'IP si nécessaire
  });

  if (error) {
    console.error('Error logging login attempt:', error);
  }
};

export const updateLastLogin = async (userId: string) => {
  const { error } = await supabase
    .from('users')
    .update({ last_login: new Date().toISOString() })
    .eq('id', userId);

  if (error) {
    console.error('Error updating last login:', error);
  }
}; 