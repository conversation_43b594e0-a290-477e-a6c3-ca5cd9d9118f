import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { StepIndicator } from '@/components/ui/step-indicator';
import { MandataireForm } from '@/components/contract/MandataireForm';
import { ShareContract } from '@/components/contract/ShareContract';
import { MandantForm } from '@/components/contract/MandantForm';
import { ContractArticles } from '@/components/contract/ContractArticles';
import { SignatureForm } from '@/components/contract/SignatureForm';
import { ContractSummary } from '@/components/contract/ContractSummary';
import { useAuth } from '@/hooks/useAuth';
import { useContracts } from '@/hooks/useContracts';
import { Contract, MandataireData, MandantData } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';

const steps = ['Mandataire', 'Partage', 'Mandant', 'Contrat', 'Signature', 'Récapitulatif'];

export function ContractPage() {
  const [match, params] = useRoute('/contract/:action');
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { 
    createContract, 
    updateContract, 
    getContract 
  } = useContracts(user?.id);

  const [currentStep, setCurrentStep] = useState(1);
  const [contract, setContract] = useState<Contract | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    console.log('ContractPage useEffect - isAuthenticated:', isAuthenticated, 'authLoading:', authLoading, 'match:', match, 'params:', params);

    // Don't redirect if still loading authentication state
    if (authLoading) {
      console.log('Still loading auth state, waiting...');
      return;
    }

    if (!isAuthenticated) {
      console.log('Not authenticated, redirecting to home');
      setLocation('/');
      return;
    }

    if (match && params) {
      const { action } = params;
      console.log('Action:', action);

      // Vérifier s'il y a un paramètre step dans l'URL
      const urlParams = new URLSearchParams(window.location.search);
      const stepParam = urlParams.get('step');
      const requestedStep = stepParam ? parseInt(stepParam) : null;

      if (action === 'new') {
        // Start new contract
        console.log('Starting new contract');
        setCurrentStep(1);
        setContract(null);
        return;
      }

      // Load existing contract only if we don't have it or it's different
      if (!contract || contract.id !== action) {
        console.log('Loading existing contract:', action);
        loadContract(action, requestedStep);
      } else if (requestedStep && requestedStep !== currentStep) {
        // Si le contrat est déjà chargé mais qu'on demande une étape spécifique
        console.log(`Changing step to requested step: ${requestedStep}`);
        setCurrentStep(requestedStep);
      }
    }
  }, [match, params, isAuthenticated, authLoading, setLocation, contract, currentStep]);

  const loadContract = async (contractId: string, requestedStep: number | null = null) => {
    // Prevent loading if already loading or if we already have this contract
    if (isLoading || (contract && contract.id === contractId)) {
      return;
    }

    setIsLoading(true);
    try {
      const existingContract = await getContract(contractId);
      if (existingContract) {
        setContract(existingContract);

        // Si une étape spécifique est demandée dans l'URL, l'utiliser
        if (requestedStep) {
          console.log(`Setting step to requested step: ${requestedStep}`);
          setCurrentStep(requestedStep);
        } else {
          // Sinon, déterminer l'étape en fonction du statut du contrat
          if (existingContract.status === 'valide') {
            setCurrentStep(5);
          } else if (existingContract.mandantData) {
            setCurrentStep(4);
          } else if (existingContract.status === 'accepte') {
            setCurrentStep(3);
          } else if (existingContract.mandataireData) {
            setCurrentStep(2);
          } else {
            setCurrentStep(1);
          }
        }
      } else {
        alert('Contrat non trouvé');
        setLocation('/contracts');
      }
    } catch (error) {
      console.error('Error loading contract:', error);
      setLocation('/contracts');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMandataireSubmit = async (data: MandataireData, selectedMandantId?: string) => {
    try {
      console.log('Submitting mandataire data:', data);
      console.log('Selected mandant ID:', selectedMandantId);

      // Créer le contrat avec les données du mandataire
      const newContract = await createContract({ mandataireData: data, userId: user?.id });

      if (!newContract) {
        throw new Error('Failed to create contract');
      }

      // Si un mandant a été sélectionné, associer son ID au contrat
      if (selectedMandantId) {
        console.log(`Associating contract with mandant ID: ${selectedMandantId}`);

        // Simuler la mise à jour du contrat avec l'ID du mandant
        const updatedContract = await updateContract({
          id: newContract.id,
          updates: {
            mandantId: selectedMandantId,
            status: 'en_attente'
          }
        });

        if (updatedContract) {
          setContract(updatedContract);
        }
      }

      console.log('Contract created successfully:', newContract);
      setContract(newContract);
      // Mettre à jour l'URL avec le paramètre step=2 pour forcer l'affichage de la page de partage
      setLocation(`/contract/${newContract.id}?step=2`);
      setCurrentStep(2);
    } catch (error) {
      console.error('Error creating contract:', error);
      alert('Une erreur est survenue lors de la création du contrat. Veuillez réessayer.');
    }
  };

  const handleMandantSubmit = async (data: MandantData) => {
    if (!contract) return;

    try {
      const updatedContract = await updateContract({
        id: contract.id,
        updates: {
          mandantData: data,
          status: 'accepte'
        }
      });
      if (updatedContract) {
        setContract(updatedContract);
        setCurrentStep(4);
      }
    } catch (error) {
      console.error('Error updating contract:', error);
    }
  };

  const handleArticlesAccept = async () => {
    // Passer à l'étape de signature
    setCurrentStep(5);
  };

  const handleSignatureSubmit = async (signatureData: { type: 'image' | 'text'; value: string }) => {
    if (!contract) return;

    try {
      console.log('Submitting signature:', signatureData);

      const updatedContract = await updateContract(contract.id, {
        status: 'valide',
        signedAt: new Date().toISOString(),
        signature: signatureData
      });

      if (updatedContract) {
        setContract(updatedContract);
        // Passer à l'étape de récapitulatif
        setCurrentStep(6);
      }
    } catch (error) {
      console.error('Error signing contract:', error);
      alert('Une erreur est survenue lors de la signature du contrat. Veuillez réessayer.');
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    alert('Téléchargement PDF - À implémenter');
  };

  const handleBackToContracts = () => {
    setLocation('/contracts');
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <StepIndicator steps={steps} currentStep={currentStep} />
        </div>

        {/* Step Content */}
        {currentStep === 1 && (
          <MandataireForm
            onSubmit={handleMandataireSubmit}
            initialData={contract?.mandataireData as MandataireData}
          />
        )}

        {currentStep === 2 && contract && (
          <ShareContract
            contractId={contract.id}
            onBack={() => setCurrentStep(1)}
            onViewContracts={handleBackToContracts}
          />
        )}

        {currentStep === 3 && (
          <MandantForm
            onSubmit={handleMandantSubmit}
            onBack={() => setCurrentStep(2)}
            initialData={contract?.mandantData as MandantData}
          />
        )}

        {currentStep === 4 && (
          <ContractArticles
            onAccept={handleArticlesAccept}
            onBack={() => setCurrentStep(3)}
          />
        )}

        {currentStep === 5 && (
          <SignatureForm
            onSubmit={handleSignatureSubmit}
            onBack={() => setCurrentStep(4)}
          />
        )}

        {currentStep === 6 && contract && (
          <ContractSummary
            contract={contract}
            onPrint={handlePrint}
            onDownload={handleDownload}
            onBackToContracts={handleBackToContracts}
          />
        )}
      </div>
    </div>
  );
}
