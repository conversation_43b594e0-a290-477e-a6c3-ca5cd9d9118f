{"id": "contracts", "name": "contracts", "type": "base", "system": false, "schema": [{"id": "title", "name": "title", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "amount", "name": "amount", "type": "number", "system": false, "required": true, "unique": false, "options": {"min": 0, "max": null}}, {"id": "currency", "name": "currency", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "debtor_name", "name": "debtor_name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "debtor_email", "name": "debtor_email", "type": "email", "system": false, "required": true, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "debtor_phone", "name": "debtor_phone", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "creditor_id", "name": "creditor_id", "type": "relation", "system": false, "required": true, "unique": false, "options": {"collectionId": "users", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["first_name", "last_name", "email"]}}, {"id": "status", "name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["draft", "sent", "signed", "completed", "cancelled"]}}, {"id": "signed_at", "name": "signed_at", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}, {"id": "signature_data", "name": "signature_data", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}