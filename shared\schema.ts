import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const contracts = pgTable("contracts", {
  id: text("id").primaryKey(),
  status: text("status").notNull().default("en_attente"), // en_attente, accepte, rejete, valide
  createdAt: timestamp("created_at").defaultNow(),
  signedAt: timestamp("signed_at"),
  mandataireData: jsonb("mandataire_data").notNull(),
  mandantData: jsonb("mandant_data"),
  userId: integer("user_id").references(() => users.id),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
});

export const insertContractSchema = createInsertSchema(contracts).omit({
  createdAt: true,
  signedAt: true,
});

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Contract = typeof contracts.$inferSelect;
export type InsertContract = z.infer<typeof insertContractSchema>;

export interface MandataireData {
  nom: string;
  email: string;
  telephone: string;
  siret?: string;
  adresse: string;
  creance: string;
  montant: number;
  commission: number;
}

export interface MandantData {
  nom: string;
  email: string;
  telephone: string;
  siret?: string;
  adresse: string;
}
