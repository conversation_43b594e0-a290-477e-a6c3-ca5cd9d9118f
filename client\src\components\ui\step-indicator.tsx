import React from 'react';
import { cn } from '@/lib/utils';
import {
  FileText,
  Share2,
  CheckCircle,
  FileSignature,
  UserCheck
} from 'lucide-react';

interface StepIndicatorProps {
  steps: string[];
  currentStep: number;
  className?: string;
}

// Icônes pour chaque étape du workflow
const stepIcons = {
  'Mandataire': FileText,    // Étape 1: Informations du mandataire
  'Partage': Share2,         // Étape 2: Partage du contrat
  'Mandant': UserCheck,      // Étape 3: Informations du mandant
  'Articles': FileSignature, // Étape 4: Articles du contrat
  'Résumé': CheckCircle,     // Étape 5: Résumé et validation
  'default': FileText        // Icône par défaut
};

export function StepIndicator({ steps, currentStep, className }: Readonly<StepIndicatorProps>) {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber <= currentStep;

        // Obtenir l'icône correspondante au nom de l'étape ou utiliser l'icône par défaut
        const IconComponent = stepIcons[step as keyof typeof stepIcons] ?? stepIcons['default'];

        // Animation et style conditionnels
        const isCompleted = stepNumber < currentStep;
        const isCurrent = stepNumber === currentStep;

        return (
          <div key={`step-${index}-${step}`} className="flex items-center">
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",
                  isCompleted
                    ? "bg-green-500 text-white shadow-md scale-105"
                    : isCurrent
                      ? "bg-primary text-white shadow-lg scale-110 animate-pulse"
                      : "bg-gray-300 text-gray-600"
                )}
              >
                <IconComponent className="w-5 h-5" />
              </div>
              <span
                className={cn(
                  "text-xs font-medium mt-2 text-center max-w-[80px] transition-colors duration-300",
                  isActive ? "text-gray-900" : "text-gray-500"
                )}
              >
                {step}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className={cn(
                "flex-1 h-1 mx-4 transition-all duration-500",
                isCompleted
                  ? "bg-green-500"
                  : stepNumber < currentStep
                    ? "bg-primary"
                    : "bg-gray-200"
              )} />
            )}
          </div>
        );
      })}
    </div>
  );
}
