import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, ArrowLeft } from 'lucide-react';
import { FaWhatsapp, FaEnvelope } from 'react-icons/fa';

interface ShareContractProps {
  contractId: string;
  onBack: () => void;
  onViewContracts: () => void;
}

export function ShareContract({ contractId, onBack, onViewContracts }: ShareContractProps) {
  const contractLink = `${window.location.origin}${window.location.pathname}?contract=${contractId}`;

  const shareViaWhatsApp = () => {
    const message = `Bonjour, RecovExpert vous a envoyé un contrat de recouvrement à signer. Cliquez sur ce lien pour y accéder: ${contractLink}`;
    const whatsappUrl = `https://wa.me/0700247430?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const shareViaEmail = () => {
    // Demander l'adresse email du destinataire
    const recipientEmail = prompt('Veuillez entrer l\'adresse email du destinataire :');

    // Si l'utilisateur annule, ne rien faire
    if (!recipientEmail) return;

    const subject = encodeURIComponent('Invitation à signer un contrat RecovExpert');
    const body = encodeURIComponent(`Bonjour,

Vous avez été invité à consulter et signer un contrat de recouvrement.

Cliquez sur le lien suivant pour accéder au contrat :
${contractLink}

Cordialement,
RecovExpert`);

    // Ajouter le destinataire à l'URL mailto
    const mailtoUrl = `mailto:${encodeURIComponent(recipientEmail)}?subject=${subject}&body=${body}`;
    console.log('Opening email with URL:', mailtoUrl);

    try {
      // Ouvrir le client de messagerie par défaut
      window.open(mailtoUrl, '_blank');

      // Copier également le lien dans le presse-papiers comme solution de secours
      navigator.clipboard.writeText(contractLink)
        .then(() => {
          console.log('Link copied to clipboard as fallback');
        })
        .catch(err => {
          console.error('Failed to copy link to clipboard:', err);
        });
    } catch (error) {
      console.error('Error opening email client:', error);
      // Fallback si l'ouverture du client de messagerie échoue
      alert(`Impossible d'ouvrir votre client de messagerie. Le lien du contrat a été copié dans le presse-papiers : ${contractLink}`);
    }
  };

  const copyContractLink = async () => {
    try {
      await navigator.clipboard.writeText(contractLink);
      alert('Lien copié dans le presse-papiers !');
    } catch (error) {
      console.error('Failed to copy link:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = contractLink;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Lien copié dans le presse-papiers !');
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Partager le contrat</h2>
      <div className="text-center py-8">
        <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
          <CheckCircle className="text-green-500 mx-auto mb-4 h-12 w-12" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Contrat créé avec succès !</h3>
          <p className="text-gray-600">Partagez ce lien avec votre client pour qu'il puisse compléter ses informations.</p>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 truncate flex-1 mr-2">{contractLink}</span>
            <Button variant="ghost" size="sm" onClick={copyContractLink}>
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4 mb-6">
          <Button
            onClick={shareViaWhatsApp}
            variant="outline"
            className="flex items-center justify-center py-3 px-4 border-green-500 text-green-600 hover:bg-green-50"
          >
            <FaWhatsapp className="text-xl mr-2" />
            Partager via WhatsApp
          </Button>
          <Button
            onClick={shareViaEmail}
            variant="outline"
            className="flex items-center justify-center py-3 px-4 border-blue-500 text-blue-600 hover:bg-blue-50"
          >
            <FaEnvelope className="text-xl mr-2" />
            Partager par email
          </Button>
        </div>

        <div className="flex justify-between">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
          <div className="flex gap-2">
            <Button onClick={onViewContracts} className="bg-primary text-white hover:bg-blue-700">
              Voir mes contrats
            </Button>
            <Button onClick={() => window.location.reload()} variant="outline" className="text-primary border-primary hover:bg-blue-50">
              Actualiser le statut
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
