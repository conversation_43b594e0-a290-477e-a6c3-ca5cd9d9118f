import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';

type ConnectedAdmin = {
  id: string;
  name: string;
  email: string;
  isOnline: boolean;
  lastSeen: string;
  role: string;
};

/**
 * Hook pour gérer les administrateurs connectés
 * @returns Un objet contenant la liste des administrateurs connectés et des méthodes pour la manipuler
 */
export function useConnectedAdmins() {
  const { user } = useAuth();
  const [connectedAdmins, setConnectedAdmins] = useState<ConnectedAdmin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Memoize the loadConnectedAdmins function to prevent it from changing on every render
  const loadConnectedAdmins = useCallback(() => {
    try {
      // Mode développement : créer des admins fictifs
      const mockAdmins: ConnectedAdmin[] = [
        {
          id: user?.id || 'admin-1',
          name: (user?.first_name && user?.last_name)
            ? `${user.first_name} ${user.last_name}`
            : 'Admin RecovExpert',
          email: user?.email || '<EMAIL>',
          isOnline: true,
          lastSeen: new Date().toISOString(),
          role: 'admin'
        }
      ];

      setConnectedAdmins(prevAdmins => {
        // Only update if the data has actually changed
        if (JSON.stringify(prevAdmins) !== JSON.stringify(mockAdmins)) {
          return mockAdmins;
        }
        return prevAdmins;
      });
    } catch (error) {
      console.error('Error loading connected admins:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Load connected admins on mount and set up interval
  useEffect(() => {
    loadConnectedAdmins();

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up new interval
    intervalRef.current = setInterval(loadConnectedAdmins, 30000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [loadConnectedAdmins]);

  // Vérifier si l'utilisateur actuel est un administrateur
  const isCurrentUserAdmin = user?.role === 'admin';

  // Memoize these functions to prevent unnecessary recalculations
  const getOnlineAdminsCount = useCallback(() => {
    return connectedAdmins.filter(admin => admin.isOnline).length;
  }, [connectedAdmins]);

  const getOnlineAdmins = useCallback(() => {
    return connectedAdmins.filter(admin => admin.isOnline);
  }, [connectedAdmins]);

  const getOfflineAdmins = useCallback(() => {
    return connectedAdmins.filter(admin => !admin.isOnline);
  }, [connectedAdmins]);

  return {
    connectedAdmins,
    isLoading,
    isCurrentUserAdmin,
    getOnlineAdminsCount,
    getOnlineAdmins,
    getOfflineAdmins
  };
}
