import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertUserSchema, insertContractSchema } from "@shared/schema";
import { z } from "zod";
import { supabase } from "./index";

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth routes - Note: Le client utilise Supabase Auth directement
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: "Email et mot de passe requis" });
      }

      // Utiliser Supabase Auth pour la connexion
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return res.status(401).json({ message: "Email ou mot de passe incorrect" });
      }

      if (data.user) {
        const user = await storage.getUserByEmail(email);
        if (user) {
          res.json({ user: { id: user.id, first_name: user.first_name, last_name: user.last_name, email: user.email } });
        } else {
          res.status(404).json({ message: "Utilisateur non trouvé" });
        }
      }
    } catch (error) {
      console.error("Error during login:", error);
      res.status(500).json({ message: "Erreur lors de la connexion" });
    }
  });

  app.post("/api/auth/signup", async (req, res) => {
    try {
      const { email, password, first_name, last_name, phone, country, city, neighborhood, profession, role } = req.body;

      if (!email || !password || !first_name || !last_name) {
        return res.status(400).json({ message: "Champs requis manquants" });
      }

      // Créer l'utilisateur avec Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        return res.status(400).json({ message: error.message });
      }

      if (data.user) {
        // Créer l'entrée dans la table users
        const userData = {
          id: data.user.id,
          email,
          first_name,
          last_name,
          phone: phone || null,
          country: country || null,
          city: city || null,
          neighborhood: neighborhood || null,
          profession: profession || null,
          role: role || 'mandant',
          is_active: true,
        };

        const { error: insertError } = await supabase
          .from('users')
          .insert(userData);

        if (insertError) {
          console.error("Error inserting user data:", insertError);
          return res.status(500).json({ message: "Erreur lors de la création du profil utilisateur" });
        }

        res.json({
          user: {
            id: data.user.id,
            first_name,
            last_name,
            email
          },
          message: "Compte créé avec succès. Veuillez vérifier votre email."
        });
      }
    } catch (error) {
      console.error("Error during signup:", error);
      res.status(500).json({ message: "Erreur lors de l'inscription" });
    }
  });

  // Contract routes
  app.post("/api/contracts", async (req, res) => {
    try {
      const contractData = insertContractSchema.parse(req.body);
      const contract = await storage.createContract(contractData);
      res.json(contract);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Données invalides", errors: error.errors });
      }
      console.error("Error creating contract:", error);
      res.status(500).json({ message: "Erreur lors de la création du contrat" });
    }
  });

  app.get("/api/contracts/:id", async (req, res) => {
    try {
      const contract = await storage.getContract(req.params.id);
      if (!contract) {
        return res.status(404).json({ message: "Contrat non trouvé" });
      }
      res.json(contract);
    } catch (error) {
      console.error("Error fetching contract:", error);
      res.status(500).json({ message: "Erreur lors de la récupération du contrat" });
    }
  });

  app.put("/api/contracts/:id", async (req, res) => {
    try {
      const updates = req.body;
      const contract = await storage.updateContract(req.params.id, updates);
      if (!contract) {
        return res.status(404).json({ message: "Contrat non trouvé" });
      }
      res.json(contract);
    } catch (error) {
      console.error("Error updating contract:", error);
      res.status(500).json({ message: "Erreur lors de la mise à jour du contrat" });
    }
  });

  app.get("/api/contracts", async (req, res) => {
    try {
      const userId = req.query.userId as string;
      if (userId) {
        const contracts = await storage.getContractsByUserId(userId);
        res.json(contracts);
      } else {
        const contracts = await storage.getAllContracts();
        res.json(contracts);
      }
    } catch (error) {
      console.error("Error fetching contracts:", error);
      res.status(500).json({ message: "Erreur lors de la récupération des contrats" });
    }
  });

  // Admin routes
  app.get("/api/admin/mandants", async (req, res) => {
    try {
      const mandants = await storage.getAllMandants();
      const stats = await storage.getMandantStats();
      res.json({ mandants, stats });
    } catch (error) {
      console.error("Error fetching mandants:", error);
      res.status(500).json({ message: "Erreur lors de la récupération des mandants" });
    }
  });

  app.delete("/api/admin/mandants/:id", async (req, res) => {
    try {
      const mandantId = req.params.id;
      // TODO: Implement mandant deletion
      res.json({ message: "Mandant supprimé avec succès" });
    } catch (error) {
      console.error("Error deleting mandant:", error);
      res.status(500).json({ message: "Erreur lors de la suppression du mandant" });
    }
  });

  // Route pour nettoyer les données de test
  app.post("/api/admin/cleanup", async (req, res) => {
    try {
      const { confirmationCode } = req.body;

      // Code de sécurité pour éviter les suppressions accidentelles
      if (confirmationCode !== "CLEANUP_TEST_DATA_2024") {
        return res.status(400).json({ message: "Code de confirmation incorrect" });
      }

      console.log("🧹 Début du nettoyage des données de test...");

      // 1. Supprimer tous les utilisateurs de la table public.users
      const { error: deleteUsersError } = await supabase
        .from('users')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Garder un ID fictif pour éviter de tout supprimer

      if (deleteUsersError) {
        console.error("Erreur lors de la suppression des utilisateurs:", deleteUsersError);
      } else {
        console.log("✅ Utilisateurs supprimés de la table public.users");
      }

      // 2. Supprimer tous les contrats
      const { error: deleteContractsError } = await supabase
        .from('contracts')
        .delete()
        .neq('id', 'fake-id'); // Supprimer tous les contrats

      if (deleteContractsError) {
        console.error("Erreur lors de la suppression des contrats:", deleteContractsError);
      } else {
        console.log("✅ Contrats supprimés");
      }

      // 3. Supprimer les tentatives de connexion
      const { error: deleteLoginAttemptsError } = await supabase
        .from('login_attempts')
        .delete()
        .neq('id', 'fake-id'); // Supprimer toutes les tentatives

      if (deleteLoginAttemptsError) {
        console.error("Erreur lors de la suppression des tentatives de connexion:", deleteLoginAttemptsError);
      } else {
        console.log("✅ Tentatives de connexion supprimées");
      }

      // 4. Lister les utilisateurs auth restants (pour information)
      const { data: authUsers, error: listAuthError } = await supabase.auth.admin.listUsers();

      if (!listAuthError && authUsers) {
        console.log(`ℹ️  ${authUsers.users.length} utilisateurs restants dans auth.users`);

        // Optionnel : supprimer les utilisateurs auth (décommentez si nécessaire)
        /*
        for (const user of authUsers.users) {
          const { error: deleteAuthError } = await supabase.auth.admin.deleteUser(user.id);
          if (deleteAuthError) {
            console.error(`Erreur lors de la suppression de l'utilisateur auth ${user.id}:`, deleteAuthError);
          }
        }
        */
      }

      console.log("🎉 Nettoyage terminé !");

      res.json({
        message: "Nettoyage des données de test terminé avec succès",
        details: {
          usersDeleted: !deleteUsersError,
          contractsDeleted: !deleteContractsError,
          loginAttemptsDeleted: !deleteLoginAttemptsError,
          authUsersRemaining: authUsers?.users.length || 0
        }
      });

    } catch (error) {
      console.error("Erreur lors du nettoyage:", error);
      res.status(500).json({ message: "Erreur lors du nettoyage des données" });
    }
  });

  // Route pour vérifier l'état de la base de données
  app.get("/api/admin/database-status", async (req, res) => {
    try {
      // Compter les utilisateurs dans public.users
      const { count: publicUsersCount, error: publicUsersError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      // Compter les contrats
      const { count: contractsCount, error: contractsError } = await supabase
        .from('contracts')
        .select('*', { count: 'exact', head: true });

      // Compter les tentatives de connexion
      const { count: loginAttemptsCount, error: loginAttemptsError } = await supabase
        .from('login_attempts')
        .select('*', { count: 'exact', head: true });

      // Lister les utilisateurs auth
      const { data: authUsers, error: authUsersError } = await supabase.auth.admin.listUsers();

      res.json({
        publicUsers: publicUsersError ? 'Erreur' : publicUsersCount,
        contracts: contractsError ? 'Erreur' : contractsCount,
        loginAttempts: loginAttemptsError ? 'Erreur' : loginAttemptsCount,
        authUsers: authUsersError ? 'Erreur' : authUsers?.users.length || 0,
        authUsersList: authUsers?.users.map(u => ({ id: u.id, email: u.email, created_at: u.created_at })) || []
      });

    } catch (error) {
      console.error("Erreur lors de la vérification du statut:", error);
      res.status(500).json({ message: "Erreur lors de la vérification du statut de la base de données" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
