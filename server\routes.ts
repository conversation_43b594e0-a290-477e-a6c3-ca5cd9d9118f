import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertUserSchema, insertContractSchema } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      if (!email || !password) {
        return res.status(400).json({ message: "Email et mot de passe requis" });
      }

      const user = await storage.getUserByEmail(email);
      if (!user || user.password !== password) {
        return res.status(401).json({ message: "Email ou mot de passe incorrect" });
      }

      res.json({ user: { id: user.id, name: user.name, email: user.email } });
    } catch (error) {
      console.error("Error during login:", error);
      res.status(500).json({ message: "Erreur lors de la connexion" });
    }
  });

  app.post("/api/auth/signup", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      
      const existingUser = await storage.getUserByEmail(userData.email);
      if (existingUser) {
        return res.status(400).json({ message: "Un utilisateur avec cet email existe déjà" });
      }

      const user = await storage.createUser(userData);
      res.json({ user: { id: user.id, name: user.name, email: user.email } });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Données invalides", errors: error.errors });
      }
      console.error("Error during signup:", error);
      res.status(500).json({ message: "Erreur lors de l'inscription" });
    }
  });

  // Contract routes
  app.post("/api/contracts", async (req, res) => {
    try {
      const contractData = insertContractSchema.parse(req.body);
      const contract = await storage.createContract(contractData);
      res.json(contract);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Données invalides", errors: error.errors });
      }
      console.error("Error creating contract:", error);
      res.status(500).json({ message: "Erreur lors de la création du contrat" });
    }
  });

  app.get("/api/contracts/:id", async (req, res) => {
    try {
      const contract = await storage.getContract(req.params.id);
      if (!contract) {
        return res.status(404).json({ message: "Contrat non trouvé" });
      }
      res.json(contract);
    } catch (error) {
      console.error("Error fetching contract:", error);
      res.status(500).json({ message: "Erreur lors de la récupération du contrat" });
    }
  });

  app.put("/api/contracts/:id", async (req, res) => {
    try {
      const updates = req.body;
      const contract = await storage.updateContract(req.params.id, updates);
      if (!contract) {
        return res.status(404).json({ message: "Contrat non trouvé" });
      }
      res.json(contract);
    } catch (error) {
      console.error("Error updating contract:", error);
      res.status(500).json({ message: "Erreur lors de la mise à jour du contrat" });
    }
  });

  app.get("/api/contracts", async (req, res) => {
    try {
      const userId = req.query.userId as string;
      if (userId) {
        const contracts = await storage.getContractsByUserId(parseInt(userId));
        res.json(contracts);
      } else {
        const contracts = await storage.getAllContracts();
        res.json(contracts);
      }
    } catch (error) {
      console.error("Error fetching contracts:", error);
      res.status(500).json({ message: "Erreur lors de la récupération des contrats" });
    }
  });

  // Admin routes
  app.get("/api/admin/mandants", async (req, res) => {
    try {
      const mandants = await storage.getAllMandants();
      const stats = await storage.getMandantStats();
      res.json({ mandants, stats });
    } catch (error) {
      console.error("Error fetching mandants:", error);
      res.status(500).json({ message: "Erreur lors de la récupération des mandants" });
    }
  });

  app.delete("/api/admin/mandants/:id", async (req, res) => {
    try {
      const mandantId = req.params.id;
      // TODO: Implement mandant deletion
      res.json({ message: "Mandant supprimé avec succès" });
    } catch (error) {
      console.error("Error deleting mandant:", error);
      res.status(500).json({ message: "Erreur lors de la suppression du mandant" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
