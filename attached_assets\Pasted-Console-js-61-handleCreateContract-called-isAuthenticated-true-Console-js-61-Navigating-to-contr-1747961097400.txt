Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"<PERSON><PERSON><PERSON>","email":"koff<PERSON><PERSON><PERSON><EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: '<PERSON><PERSON><PERSON>', email: 'koffiko<PERSON><EMAIL>'}
Console.js:61 ContractPage useEffect - isAuthenticated: false match: true params: {0: 'new', action: 'new'}
Console.js:61 Not authenticated, redirecting to home
Console.js:61 Current location: /contract/new
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"<PERSON><PERSON><PERSON><PERSON><PERSON>","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: '<PERSON><PERSON><PERSON>', email: 'koff<PERSON><PERSON><PERSON><EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou <PERSON> Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 Current location: /
Console.js:61 handleViewContracts called, isAuthenticated: true
Console.js:61 Navigating to /contracts
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 ContractsListPage useEffect - isAuthenticated: false
Console.js:61 Not authenticated in ContractsListPage, redirecting to home
Console.js:61 Current location: /contracts
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 Current location: /
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 ContractPage useEffect - isAuthenticated: false match: true params: {0: 'new', action: 'new'}
Console.js:61 Not authenticated, redirecting to home
Console.js:61 Current location: /contract/new
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 Current location: /
Console.js:61 handleViewContracts called, isAuthenticated: true
Console.js:61 Navigating to /contracts
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 ContractsListPage useEffect - isAuthenticated: false
Console.js:61 Not authenticated in ContractsListPage, redirecting to home
Console.js:61 Current location: /contracts
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 Current location: /
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 ContractPage useEffect - isAuthenticated: false match: true params: {0: 'new', action: 'new'}
Console.js:61 Not authenticated, redirecting to home
Console.js:61 Current location: /contract/new
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 useAuth useEffect - savedUser: {"id":1,"name":"Koffi Kouakou Alain Constant","email":"<EMAIL>"}
Console.js:61 Setting user from localStorage: {id: 1, name: 'Koffi Kouakou Alain Constant', email: '<EMAIL>'}
Console.js:61 Current location: /
