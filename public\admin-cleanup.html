<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - Nettoyage des données</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-right: 10px;
            width: 200px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Administration - Nettoyage des données</h1>
        
        <div class="warning">
            <strong>⚠️ Attention :</strong> Cette page permet de nettoyer les données de test. 
            Utilisez-la uniquement en développement !
        </div>

        <div class="section">
            <h2>📊 État de la base de données</h2>
            <button onclick="checkDatabaseStatus()">Vérifier l'état</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>🧹 Nettoyage des données</h2>
            <p>Code de confirmation requis : <strong>CLEANUP_TEST_DATA_2024</strong></p>
            <input type="text" id="confirmationCode" placeholder="Code de confirmation" />
            <button class="danger" onclick="cleanupData()">Nettoyer les données</button>
            <div id="cleanupResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function checkDatabaseStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Vérification en cours...';

            try {
                const response = await fetch('/api/admin/database-status');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `État de la base de données :

📊 Statistiques :
- Utilisateurs (public.users) : ${data.publicUsers}
- Contrats : ${data.contracts}
- Tentatives de connexion : ${data.loginAttempts}
- Utilisateurs Auth : ${data.authUsers}

👥 Utilisateurs Auth détaillés :
${data.authUsersList.map(u => `- ${u.email} (${u.id}) - Créé le ${new Date(u.created_at).toLocaleString()}`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Erreur : ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Erreur de connexion : ${error.message}`;
            }
        }

        async function cleanupData() {
            const confirmationCode = document.getElementById('confirmationCode').value;
            const resultDiv = document.getElementById('cleanupResult');
            
            if (!confirmationCode) {
                alert('Veuillez entrer le code de confirmation');
                return;
            }

            if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les données de test ? Cette action est irréversible !')) {
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Nettoyage en cours...';

            try {
                const response = await fetch('/api/admin/cleanup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ confirmationCode })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${data.message}

Détails :
- Utilisateurs supprimés : ${data.details.usersDeleted ? 'Oui' : 'Non'}
- Contrats supprimés : ${data.details.contractsDeleted ? 'Oui' : 'Non'}
- Tentatives de connexion supprimées : ${data.details.loginAttemptsDeleted ? 'Oui' : 'Non'}
- Utilisateurs Auth restants : ${data.details.authUsersRemaining}

🎉 Vous pouvez maintenant tester l'inscription avec un email frais !`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erreur : ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erreur de connexion : ${error.message}`;
            }
        }

        // Vérifier l'état au chargement de la page
        window.onload = function() {
            checkDatabaseStatus();
        };
    </script>
</body>
</html>
