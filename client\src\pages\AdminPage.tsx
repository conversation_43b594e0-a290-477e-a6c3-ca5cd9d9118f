import React from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { ConnectedAdmins } from '@/components/admin/ConnectedAdmins';
import { Users, FileText, Settings, Shield, ChevronRight, BarChart } from 'lucide-react';

export function AdminPage() {
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { isAdmin } = useAdmin();

  // Rediriger si non authentifié ou non admin
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Temporairement désactivé pour permettre l'accès en mode développement
  // if (!isAuthenticated || !isAdmin) {
  //   setLocation('/');
  //   return null;
  // }

  // Afficher un message si pas admin mais permettre l'accès
  if (!isAdmin && isAuthenticated) {
    console.log('⚠️ Utilisateur connecté mais pas admin, accès autorisé en mode dev');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Administration</h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Bienvenue, {user?.name}. Gérez votre plateforme RecovExpert.
            </p>
          </div>

          <div className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/30 px-4 py-2 rounded-lg">
            <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Accès Administrateur
            </span>
          </div>
        </div>

        {/* Affichage des administrateurs connectés */}
        <ConnectedAdmins />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Carte Mandants */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Users className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
                Gestion des Mandants
              </CardTitle>
              <CardDescription>
                Consultez et gérez les mandants
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>• Voir la liste des mandants</p>
                <p>• Consulter les détails des mandants</p>
                <p>• Gérer les accès des mandants</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => {
                  console.log('Clic sur le bouton Accéder - Redirection vers /mandants');
                  setLocation('/mandants');
                }}
              >
                Accéder
                <ChevronRight className="h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>

          {/* Carte Contrats */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <FileText className="mr-2 h-5 w-5 text-green-600 dark:text-green-400" />
                Gestion des Contrats
              </CardTitle>
              <CardDescription>
                Consultez et gérez tous les contrats
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>• Voir tous les contrats</p>
                <p>• Suivre l'état des contrats</p>
                <p>• Gérer les contrats en attente</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => setLocation('/contracts')}
              >
                Accéder
                <ChevronRight className="h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>

          {/* Carte Statistiques */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <BarChart className="mr-2 h-5 w-5 text-purple-600 dark:text-purple-400" />
                Statistiques
              </CardTitle>
              <CardDescription>
                Consultez les statistiques de la plateforme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>• Nombre de contrats signés</p>
                <p>• Taux de conversion</p>
                <p>• Activité des mandants</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => setLocation('/stats')}
              >
                Accéder
                <ChevronRight className="h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* Section Paramètres */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Settings className="mr-2 h-5 w-5 text-gray-600 dark:text-gray-400" />
            Paramètres du système
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="outline"
              className="justify-start text-gray-700 dark:text-gray-300"
              onClick={() => setLocation('/settings/general')}
            >
              Paramètres généraux
            </Button>

            <Button
              variant="outline"
              className="justify-start text-gray-700 dark:text-gray-300"
              onClick={() => setLocation('/settings/users')}
            >
              Gestion des utilisateurs
            </Button>

            <Button
              variant="outline"
              className="justify-start text-gray-700 dark:text-gray-300"
              onClick={() => setLocation('/settings/templates')}
            >
              Modèles de contrats
            </Button>

            <Button
              variant="outline"
              className="justify-start text-gray-700 dark:text-gray-300"
              onClick={() => setLocation('/settings/notifications')}
            >
              Paramètres de notifications
            </Button>
          </div>
        </div>

        {/* Note d'information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <p className="text-sm text-blue-700 dark:text-blue-300">
            <strong>Note:</strong> Cette interface d'administration est en cours de développement.
            Certaines fonctionnalités peuvent ne pas être encore disponibles.
          </p>
        </div>
      </div>
    </div>
  );
}
