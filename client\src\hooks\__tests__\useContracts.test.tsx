import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useContracts } from '../useContracts';
import { apiRequest } from '@/lib/queryClient';
import { Contract, MandataireData } from '@/types';

// Mock API requests
jest.mock('@/lib/queryClient', () => ({
  apiRequest: jest.fn(),
}));

const queryClient = new QueryClient();
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('useContracts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
  });

  it('should fetch contract by id', async () => {
    const mockContract: Contract = {
      id: 'contract_1',
      status: 'en_attente',
      mandataireData: { nom: 'Test' } as MandataireData,
      userId: 'user_1',
      createdAt: new Date().toISOString(),
    };

    (apiRequest as jest.Mock).mockResolvedValueOnce({
      json: async () => mockContract,
    });

    const { result } = renderHook(() => useContracts('user_1'), { wrapper });

    await act(async () => {
      const contract = await result.current.getContract('contract_1');
      expect(contract).toEqual(mockContract);
    });

    expect(apiRequest).toHaveBeenCalledWith('GET', '/api/contracts/contract_1');
  });

  it('should create new contract', async () => {
    const newContractData = {
      mandataireData: { nom: 'Test' } as MandataireData,
      userId: 'user_1'
    };

    const mockContract: Contract = {
      id: 'contract_1',
      status: 'en_attente',
      ...newContractData,
      createdAt: new Date().toISOString(),
    };

    (apiRequest as jest.Mock).mockResolvedValueOnce({
      json: async () => mockContract,
    });

    const { result } = renderHook(() => useContracts('user_1'), { wrapper });

    await act(async () => {
      const contract = await result.current.createContract(newContractData);
      expect(contract).toEqual(mockContract);
    });

    expect(apiRequest).toHaveBeenCalledWith(
      'POST', 
      '/api/contracts',
      expect.objectContaining({
        ...newContractData,
        status: 'en_attente'
      })
    );
  });

  it('should update contract', async () => {
    const updates = { status: 'accepte' };
    const mockContract: Contract = {
      id: 'contract_1',
      status: 'accepte',
      mandataireData: { nom: 'Test' } as MandataireData,
      userId: 'user_1',
      createdAt: new Date().toISOString(),
    };

    (apiRequest as jest.Mock).mockResolvedValueOnce({
      json: async () => mockContract,
    });

    const { result } = renderHook(() => useContracts('user_1'), { wrapper });

    await act(async () => {
      const contract = await result.current.updateContract('contract_1', updates);
      expect(contract).toEqual(mockContract);
    });

    expect(apiRequest).toHaveBeenCalledWith(
      'PUT',
      '/api/contracts/contract_1',
      updates
    );
  });

  it('should handle API errors', async () => {
    (apiRequest as jest.Mock).mockRejectedValueOnce(new Error('API Error'));

    const { result } = renderHook(() => useContracts('user_1'), { wrapper });

    await act(async () => {
      await expect(result.current.getContract('contract_1')).rejects.toThrow('API Error');
    });
  });
});
