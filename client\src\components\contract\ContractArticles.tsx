import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { ArrowLeft, ArrowRight, FileText, Check, AlertCircle } from 'lucide-react';
import { ContractArticle } from '@/types';
import { cn } from '@/lib/utils';

interface ContractArticlesProps {
  onAccept: () => void;
  onBack: () => void;
}

const contractArticles: ContractArticle[] = [
  {
    title: "Article 1 - Objet du mandat",
    content: "Le Mandant confie au Mandataire le recouvrement amiable de la créance décrite ci-dessus. Le Mandataire s'engage à entreprendre toutes les démarches nécessaires pour obtenir le paiement de ladite créance dans les meilleurs délais."
  },
  {
    title: "Article 2 - Obligations du Mandataire",
    content: "Le Mandataire s'engage à agir avec diligence et professionnalisme. Il devra tenir informé le Mandant de l'évolution des démarches entreprises et des résultats obtenus."
  },
  {
    title: "Article 3 - Rémunération",
    content: "La rémunération du Mandataire est fixée à un pourcentage du montant recouvré, tel que spécifié dans le contrat. Cette commission est due uniquement en cas de recouvrement effectif."
  },
  {
    title: "Article 4 - Durée du mandat",
    content: "Le présent mandat est conclu pour une durée de 12 mois à compter de sa signature, renouvelable par accord mutuel des parties."
  },
  {
    title: "Article 5 - Moyens employés",
    content: "Le Mandataire s'engage à n'utiliser que des moyens légaux et éthiques pour le recouvrement. Aucune pratique déloyale ou intimidante ne sera tolérée."
  },
  {
    title: "Article 6 - Confidentialité",
    content: "Le Mandataire s'engage à maintenir la confidentialité sur toutes les informations communiquées dans le cadre de ce mandat."
  },
  {
    title: "Article 7 - Résiliation",
    content: "Chaque partie peut résilier le présent contrat moyennant un préavis écrit de 30 jours. En cas de résiliation, les frais déjà engagés restent dus."
  },
  {
    title: "Article 8 - Responsabilité",
    content: "Le Mandataire ne peut être tenu responsable de l'insolvabilité du débiteur ou de l'impossibilité de recouvrer la créance pour des raisons indépendantes de sa volonté."
  },
  {
    title: "Article 9 - Frais",
    content: "Tous les frais engagés dans le cadre du recouvrement (frais de courrier, de déplacement, etc.) sont à la charge du Mandant, sauf accord contraire."
  },
  {
    title: "Article 10 - Litiges",
    content: "Tout litige relatif au présent contrat sera soumis à la juridiction compétente du lieu de résidence du Mandant."
  },
  {
    title: "Article 11 - Dispositions générales",
    content: "Le présent contrat constitue l'intégralité de l'accord entre les parties. Toute modification doit faire l'objet d'un avenant écrit et signé des deux parties."
  }
];

export function ContractArticles({ onAccept, onBack }: ContractArticlesProps) {
  const [hasRead, setHasRead] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Articles du Contrat de Recouvrement</h2>

      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center text-blue-700 mb-2">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p className="font-medium">Veuillez lire attentivement les articles du contrat ci-dessous</p>
        </div>
        <p className="text-sm text-blue-600">
          Vous devrez confirmer avoir lu et accepté les termes du contrat pour continuer.
        </p>
      </div>

      <Accordion type="multiple" className="space-y-4 mb-8">
        {contractArticles.map((article, index) => (
          <AccordionItem
            key={`article-${article.title}`}
            value={`article-${index}`}
            className="border border-gray-200 rounded-lg"
          >
            <AccordionTrigger className="px-4 py-3 text-left bg-gray-50 hover:bg-gray-100">
              <span className="font-medium text-gray-900">{article.title}</span>
            </AccordionTrigger>
            <AccordionContent className="px-4 py-3 border-t border-gray-200">
              <p className="text-gray-700 text-sm leading-relaxed">{article.content}</p>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>

      <div className="space-y-4 mb-8">
        <div className={cn(
          "flex items-center p-4 rounded-lg border",
          hasRead ? "bg-green-50 border-green-200" : "bg-gray-50 border-gray-200"
        )}>
          <Checkbox
            id="hasRead"
            checked={hasRead}
            onCheckedChange={(checked) => setHasRead(checked as boolean)}
            className={cn("mr-3", hasRead && "text-green-500")}
          />
          <label htmlFor="hasRead" className={cn("text-sm", hasRead ? "text-green-700" : "text-gray-700")}>
            J'ai lu tous les articles du contrat de recouvrement
          </label>
          {hasRead && <Check className="ml-auto h-4 w-4 text-green-500" />}
        </div>

        <div className={cn(
          "flex items-center p-4 rounded-lg border",
          acceptedTerms ? "bg-green-50 border-green-200" : "bg-gray-50 border-gray-200"
        )}>
          <Checkbox
            id="acceptTerms"
            checked={acceptedTerms}
            onCheckedChange={(checked) => setAcceptedTerms(checked as boolean)}
            disabled={!hasRead}
            className={cn(
              "mr-3",
              acceptedTerms && "text-green-500",
              !hasRead && "opacity-50"
            )}
          />
          <label
            htmlFor="acceptTerms"
            className={cn(
              "text-sm",
              acceptedTerms ? "text-green-700" : "text-gray-700",
              !hasRead && "opacity-50"
            )}
          >
            J'accepte les termes du contrat décrits dans les articles
          </label>
          {acceptedTerms && <Check className="ml-auto h-4 w-4 text-green-500" />}
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <Button
          onClick={onAccept}
          disabled={!hasRead || !acceptedTerms}
          className={cn(
            "px-8 py-3",
            hasRead && acceptedTerms
              ? "bg-primary text-white hover:bg-blue-700"
              : "bg-gray-400 text-white cursor-not-allowed"
          )}
        >
          Procéder à la signature
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
