import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Contract, MandataireData, MandantData } from '@/types';
import { generateContractId } from '@/lib/utils';
import { apiRequest } from '@/lib/queryClient';

type ApiError = {
  message: string;
  status?: number;
  details?: unknown;
};

export function useContracts(userId?: number) {
  const queryClient = useQueryClient();

  const contractsQuery = useQuery<Contract[], ApiError>({
    queryKey: ['contracts', userId],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/contracts?userId=${userId}`);
      return await response.json();
    },
    enabled: !!userId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const createContractMutation = useMutation<Contract, ApiError, { mandataireData: MandataireData; userId?: number }>({
    mutationFn: async ({ mandataireData, userId }) => {
      const contractData = {
        id: generateContractId(),
        status: 'en_attente' as const,
        mandataireData,
        mandantData: null,
        userId: userId ?? null,
        createdAt: new Date().toISOString(),
      };

      const response = await apiRequest('POST', '/api/contracts', contractData);
      return await response.json();
    },
    onSuccess: (newContract) => {
      queryClient.setQueryData<Contract[]>(['contracts', userId], (old) => 
        old ? [...old, newContract] : [newContract]
      );
    },
  });

  const updateContractMutation = useMutation<Contract, ApiError, { id: string; updates: Partial<Contract> }>({
    mutationFn: async ({ id, updates }) => {
      const response = await apiRequest('PUT', `/api/contracts/${id}`, updates);
      return await response.json();
    },
    onSuccess: (updatedContract) => {
      queryClient.setQueryData<Contract[]>(['contracts', userId], (old) =>
        old?.map(contract => 
          contract.id === updatedContract.id ? updatedContract : contract
        )
      );
    },
  });

  const getContractQuery = (contractId: string) => useQuery<Contract | null, ApiError>({
    queryKey: ['contract', contractId],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/contracts/${contractId}`);
      return await response.json();
    },
    initialData: () => {
      // Try to get the contract from the contracts query cache first
      const contracts = queryClient.getQueryData<Contract[]>(['contracts', userId]);
      return contracts?.find(c => c.id === contractId) ?? null;
    },
  });

  return {
    contracts: contractsQuery.data || [],
    isLoading: contractsQuery.isLoading,
    error: contractsQuery.error,
    createContract: createContractMutation.mutateAsync,
    updateContract: updateContractMutation.mutateAsync,
    getContract: getContractQuery,
    refreshContracts: () => queryClient.invalidateQueries({ queryKey: ['contracts', userId] }),
  };
}
