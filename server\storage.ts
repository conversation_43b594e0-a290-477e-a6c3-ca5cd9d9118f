import { users, contracts, type User, type InsertUser, type Contract, type InsertContract } from "@shared/schema";
import { type SupabaseUser, type InsertSupabaseUser, type SupabaseContract, type MandataireData, type MandantData } from "@shared/supabase-schema";
import { supabase } from "./index";

export interface IStorage {
  // User operations
  getUser(id: string): Promise<SupabaseUser | undefined>;
  getUserByEmail(email: string): Promise<SupabaseUser | undefined>;
  createUser(user: InsertSupabaseUser): Promise<SupabaseUser>;

  // Contract operations
  getContract(id: string): Promise<SupabaseContract | undefined>;
  getContractsByUserId(userId: string): Promise<SupabaseContract[]>;
  createContract(contract: any): Promise<SupabaseContract>;
  updateContract(id: string, updates: Partial<SupabaseContract>): Promise<SupabaseContract | undefined>;
  getAllContracts(): Promise<SupabaseContract[]>;

  // Mandant operations
  getAllMandants(): Promise<MandantData[]>;
  getMandantStats(): Promise<{
    total: number;
    active: number;
    newThisMonth: number;
    totalContracts: number;
  }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private contracts: Map<string, Contract>;
  private currentUserId: number;

  constructor() {
    this.users = new Map();
    this.contracts = new Map();
    this.currentUserId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email === email,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = {
      ...insertUser,
      id,
      createdAt: new Date()
    };
    this.users.set(id, user);
    return user;
  }

  async getContract(id: string): Promise<Contract | undefined> {
    return this.contracts.get(id);
  }

  async getContractsByUserId(userId: number): Promise<Contract[]> {
    return Array.from(this.contracts.values()).filter(
      (contract) => contract.userId === userId
    );
  }

  async createContract(contract: InsertContract): Promise<Contract> {
    const newContract: Contract = {
      id: contract.id,
      status: contract.status || 'en_attente',
      createdAt: new Date(),
      signedAt: null,
      mandataireData: contract.mandataireData,
      mandantData: contract.mandantData || null,
      userId: contract.userId || null
    };
    this.contracts.set(contract.id, newContract);
    return newContract;
  }

  async updateContract(id: string, updates: Partial<Contract>): Promise<Contract | undefined> {
    const contract = this.contracts.get(id);
    if (!contract) return undefined;

    const updatedContract = { ...contract, ...updates };
    this.contracts.set(id, updatedContract);
    return updatedContract;
  }

  async getAllContracts(): Promise<Contract[]> {
    return Array.from(this.contracts.values());
  }

  async getAllMandants(): Promise<MandantData[]> {
    const mandants = new Set<MandantData>();
    for (const contract of this.contracts.values()) {
      if (contract.mandantData) {
        mandants.add(contract.mandantData);
      }
    }
    return Array.from(mandants);
  }

  async getMandantStats(): Promise<{
    total: number;
    active: number;
    newThisMonth: number;
    totalContracts: number;
  }> {
    const mandants = await this.getAllMandants();
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return {
      total: mandants.length,
      active: mandants.filter(m => m.isActive).length,
      newThisMonth: mandants.filter(m => new Date(m.createdAt) >= firstDayOfMonth).length,
      totalContracts: this.contracts.size
    };
  }
}

// Implémentation Supabase
export class SupabaseStorage implements IStorage {
  async getUser(id: string): Promise<SupabaseUser | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return data as SupabaseUser;
  }

  async getUserByEmail(email: string): Promise<SupabaseUser | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !data) return undefined;
    return data as SupabaseUser;
  }

  async createUser(insertUser: InsertSupabaseUser): Promise<SupabaseUser> {
    const { data, error } = await supabase
      .from('users')
      .insert(insertUser)
      .select()
      .single();

    if (error) throw error;
    return data as SupabaseUser;
  }

  async getContract(id: string): Promise<SupabaseContract | undefined> {
    const { data, error } = await supabase
      .from('contracts')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return data as SupabaseContract;
  }

  async getContractsByUserId(userId: string): Promise<SupabaseContract[]> {
    const { data, error } = await supabase
      .from('contracts')
      .select('*')
      .eq('user_id', userId);

    if (error) return [];
    return data as SupabaseContract[];
  }

  async createContract(contract: any): Promise<SupabaseContract> {
    const { data, error } = await supabase
      .from('contracts')
      .insert(contract)
      .select()
      .single();

    if (error) throw error;
    return data as SupabaseContract;
  }

  async updateContract(id: string, updates: Partial<SupabaseContract>): Promise<SupabaseContract | undefined> {
    const { data, error } = await supabase
      .from('contracts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;
    return data as SupabaseContract;
  }

  async getAllContracts(): Promise<SupabaseContract[]> {
    const { data, error } = await supabase
      .from('contracts')
      .select('*');

    if (error) return [];
    return data as SupabaseContract[];
  }

  async getAllMandants(): Promise<MandantData[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', 'mandant');

    if (error) return [];
    return data.map(user => ({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
      phone: user.phone || '',
      company: user.profession || '',
      siret: '',
      address: `${user.city || ''}, ${user.country || ''}`.trim(),
      created_at: user.created_at,
      last_login: user.last_login,
      is_active: user.is_active,
      contracts: { total: 0, active: 0, completed: 0 }
    })) as MandantData[];
  }

  async getMandantStats(): Promise<{
    total: number;
    active: number;
    newThisMonth: number;
    totalContracts: number;
  }> {
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .eq('role', 'mandant');

    const { data: contracts, error: contractsError } = await supabase
      .from('contracts')
      .select('*');

    if (usersError || contractsError) {
      return { total: 0, active: 0, newThisMonth: 0, totalContracts: 0 };
    }

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return {
      total: users.length,
      active: users.filter(u => u.is_active).length,
      newThisMonth: users.filter(u => new Date(u.created_at) >= firstDayOfMonth).length,
      totalContracts: contracts.length
    };
  }
}

// Utiliser Supabase au lieu de MemStorage
export const storage = new SupabaseStorage();
