import { users, contracts, type User, type InsertUser, type Contract, type InsertContract, type MandataireData, type MandantData } from "@shared/schema";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Contract operations
  getContract(id: string): Promise<Contract | undefined>;
  getContractsByUserId(userId: number): Promise<Contract[]>;
  createContract(contract: InsertContract): Promise<Contract>;
  updateContract(id: string, updates: Partial<Contract>): Promise<Contract | undefined>;
  getAllContracts(): Promise<Contract[]>;

  // Mandant operations
  getAllMandants(): Promise<MandantData[]>;
  getMandantStats(): Promise<{
    total: number;
    active: number;
    newThisMonth: number;
    totalContracts: number;
  }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private contracts: Map<string, Contract>;
  private currentUserId: number;

  constructor() {
    this.users = new Map();
    this.contracts = new Map();
    this.currentUserId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email === email,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { 
      ...insertUser, 
      id,
      createdAt: new Date()
    };
    this.users.set(id, user);
    return user;
  }

  async getContract(id: string): Promise<Contract | undefined> {
    return this.contracts.get(id);
  }

  async getContractsByUserId(userId: number): Promise<Contract[]> {
    return Array.from(this.contracts.values()).filter(
      (contract) => contract.userId === userId
    );
  }

  async createContract(contract: InsertContract): Promise<Contract> {
    const newContract: Contract = {
      id: contract.id,
      status: contract.status || 'en_attente',
      createdAt: new Date(),
      signedAt: null,
      mandataireData: contract.mandataireData,
      mandantData: contract.mandantData || null,
      userId: contract.userId || null
    };
    this.contracts.set(contract.id, newContract);
    return newContract;
  }

  async updateContract(id: string, updates: Partial<Contract>): Promise<Contract | undefined> {
    const contract = this.contracts.get(id);
    if (!contract) return undefined;
    
    const updatedContract = { ...contract, ...updates };
    this.contracts.set(id, updatedContract);
    return updatedContract;
  }

  async getAllContracts(): Promise<Contract[]> {
    return Array.from(this.contracts.values());
  }

  async getAllMandants(): Promise<MandantData[]> {
    const mandants = new Set<MandantData>();
    for (const contract of this.contracts.values()) {
      if (contract.mandantData) {
        mandants.add(contract.mandantData);
      }
    }
    return Array.from(mandants);
  }

  async getMandantStats(): Promise<{
    total: number;
    active: number;
    newThisMonth: number;
    totalContracts: number;
  }> {
    const mandants = await this.getAllMandants();
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return {
      total: mandants.length,
      active: mandants.filter(m => m.isActive).length,
      newThisMonth: mandants.filter(m => new Date(m.createdAt) >= firstDayOfMonth).length,
      totalContracts: this.contracts.size
    };
  }
}

export const storage = new MemStorage();
