import React, { useState } from 'react';
import { User } from '@/types';
import { useConnectedAdmins } from '@/hooks/useConnectedAdmins';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Shield, Users, Clock, ChevronDown, ChevronUp, UserCheck, UserX } from 'lucide-react';
import { formatDateTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

export function ConnectedAdmins() {
  const { 
    connectedAdmins, 
    isLoading, 
    isCurrentUserAdmin,
    getOnlineAdminsCount, 
    getOnlineAdmins, 
    getOfflineAdmins 
  } = useConnectedAdmins();
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('online');
  
  // Si l'utilisateur n'est pas un administrateur, ne pas afficher ce composant
  if (!isCurrentUserAdmin) {
    return null;
  }
  
  const onlineAdmins = getOnlineAdmins();
  const offlineAdmins = getOfflineAdmins();
  const onlineCount = getOnlineAdminsCount();
  
  // Afficher un message de chargement
  if (isLoading) {
    return (
      <Card className="w-full mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <Shield className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
            Administrateurs connectés
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Chargement...</span>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full mb-6">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center">
            <Shield className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
            Administrateurs connectés
          </CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-8 w-8 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
        <CardDescription>
          {onlineCount} administrateur{onlineCount > 1 ? 's' : ''} en ligne
        </CardDescription>
      </CardHeader>
      
      {isExpanded && (
        <>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="online" className="flex items-center">
                  <UserCheck className="h-4 w-4 mr-2" />
                  En ligne ({onlineAdmins.length})
                </TabsTrigger>
                <TabsTrigger value="offline" className="flex items-center">
                  <UserX className="h-4 w-4 mr-2" />
                  Hors ligne ({offlineAdmins.length})
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="online">
                {onlineAdmins.length === 0 ? (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    Aucun administrateur en ligne
                  </div>
                ) : (
                  <div className="space-y-3">
                    {onlineAdmins.map((admin) => (
                      <AdminItem 
                        key={admin.adminId} 
                        admin={admin} 
                        isCurrentUser={admin.email === connectedAdmins.find(a => a.email === admin.email)?.email}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="offline">
                {offlineAdmins.length === 0 ? (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    Aucun administrateur hors ligne
                  </div>
                ) : (
                  <div className="space-y-3">
                    {offlineAdmins.map((admin) => (
                      <AdminItem 
                        key={admin.adminId} 
                        admin={admin} 
                        isCurrentUser={admin.email === connectedAdmins.find(a => a.email === admin.email)?.email}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
          
          <CardFooter className="pt-0">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Dernière mise à jour: {formatDateTime(new Date())}
            </div>
          </CardFooter>
        </>
      )}
    </Card>
  );
}

interface AdminItemProps {
  admin: User;
  isCurrentUser: boolean;
}

function AdminItem({ admin, isCurrentUser }: AdminItemProps) {
  return (
    <div className={cn(
      "flex items-center justify-between p-3 rounded-lg",
      admin.isOnline 
        ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
        : "bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
    )}>
      <div className="flex items-center">
        <div className={cn(
          "h-8 w-8 rounded-full flex items-center justify-center mr-3",
          admin.isOnline 
            ? "bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300"
            : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
        )}>
          <Users className="h-4 w-4" />
        </div>
        <div>
          <div className="flex items-center">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {admin.name}
            </p>
            {isCurrentUser && (
              <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-1.5 py-0.5 rounded-full">
                Vous
              </span>
            )}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {admin.email}
          </p>
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
          <Clock className="h-3 w-3 mr-1" />
          <span>
            {admin.lastLogin ? formatDateTime(admin.lastLogin).split(' ')[1] : 'N/A'}
          </span>
        </div>
        <div className={cn(
          "ml-3 h-2 w-2 rounded-full",
          admin.isOnline 
            ? "bg-green-500 dark:bg-green-400"
            : "bg-gray-300 dark:bg-gray-600"
        )}></div>
      </div>
    </div>
  );
}
