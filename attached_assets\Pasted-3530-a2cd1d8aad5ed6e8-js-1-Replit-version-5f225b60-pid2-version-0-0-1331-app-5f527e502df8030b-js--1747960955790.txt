3530-a2cd1d8aad5ed6e8.js:1 Replit version 5f225b60, pid2 version 0.0.1331
_app-5f527e502df8030b.js:38 
      /////////////////////
   ///////////////////////////
  ////////@@@@@@@//////////////
  ////////@@@@@@@//////////////
  ////////@@@@@@@//////////////        Curious and driven?
  ///////////////@@@@@@@///////        Work with us!
  ///////////////@@@@@@@///////
  ///////////////@@@@@@@///////        https://join.replit.com/hack.with.us
  ////////@@@@@@@//////////////
  ////////@@@@@@@//////////////
  ////////@@@@@@#//////////////
   ///////////////////////////
      /////////////////////
      
stallwart.build.js:1 stallwart: failed ping 1
_app-5f527e502df8030b.js:38 [LaunchDarkly] LaunchDarkly client initialized
_app-5f527e502df8030b.js:38 [LaunchDarkly] Opening stream connection to https://clientstream.launchdarkly.com/eval/62b35a865152ab14c5942820/eyJrZXkiOiI0MzAxNTQ0NSIsImtpbmQiOiJ1c2VyIiwiYW5vbnltb3VzIjpmYWxzZX0
_app-5f527e502df8030b.js:38 [LaunchDarkly] Closing stream connection
_app-5f527e502df8030b.js:38 [LaunchDarkly] Opening stream connection to https://clientstream.launchdarkly.com/eval/62b35a865152ab14c5942820/eyJrZXkiOiI0MzAxNTQ0NSIsImtpbmQiOiJ1c2VyIiwibmFtZSI6ImtvZmZpa291YWtvdWFsYSIsInJvbGVzIjpbXSwiZW1haWwiOiJrb2ZmaWtvdWFrb3VhbGFpbmNvbnN0YW50QGdtYWlsLmNvbSIsInRpbWVfY3JlYXRlZCI6MTc0Nzk0MDI0NjQyMSwiX21ldGEiOnsicHJpdmF0ZUF0dHJpYnV0ZXMiOlsiZW1haWwiXX19
stallwart.build.js:1 stallwart: failed ping 1
/~:1 Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.
_app-5f527e502df8030b.js:38 WARN: A component changed from uncontrolled to controlled.
(anonymous) @ _app-5f527e502df8030b.js:38
_app-5f527e502df8030b.js:38 WARN: A component changed from uncontrolled to controlled.
(anonymous) @ _app-5f527e502df8030b.js:38
_app-5f527e502df8030b.js:38 [LaunchDarkly] Closing stream connection
_app-5f527e502df8030b.js:38 [LaunchDarkly] Opening stream connection to https://clientstream.launchdarkly.com/eval/62b35a865152ab14c5942820/eyJrZXkiOiI0MzAxNTQ0NSIsImtpbmQiOiJ1c2VyIiwibmFtZSI6ImtvZmZpa291YWtvdWFsYSIsInJvbGVzIjpbImludGVudF9zdHVkZW50Il0sImVtYWlsIjoia29mZmlrb3Vha291YWxhaW5jb25zdGFudEBnbWFpbC5jb20iLCJ0aW1lX2NyZWF0ZWQiOjE3NDc5NDAyNDY0MjEsIl9tZXRhIjp7InByaXZhdGVBdHRyaWJ1dGVzIjpbImVtYWlsIl19fQ
_app-5f527e502df8030b.js:38 [LaunchDarkly] Closing stream connection
/~:1 Blocked aria-hidden on an element because its descendant retained focus. The focus must not be hidden from assistive technology users. Avoid using aria-hidden on a focused element or its ancestor. Consider using the inert attribute instead, which will also prevent focus. For more details, see the aria-hidden section of the WAI-ARIA specification at https://w3c.github.io/aria/#aria-hidden.
Element with focus: <div.cm-content cm-lineWrapping>
Ancestor with aria-hidden: <div. useView_view__C2mnv css-17qdd0f Surface_surfaceRoot__TeA2u> <div class=​" useView_view__C2mnv css-17qdd0f Surface_surfaceRoot__TeA2u" aria-hidden=​"true" style=​"--useView--gap:​ 4px;​ border-color:​ var(--outline-dimmer)​;​ --focusedBorderColor:​ var(--accent-primary-default)​;​ --hoverBorderColor:​ var(--accent-primary-default)​;​">​…​</div>​
_app-5f527e502df8030b.js:38 [LaunchDarkly] Opening stream connection to https://clientstream.launchdarkly.com/eval/62b35a865152ab14c5942820/eyJrZXkiOiI0MzAxNTQ0NSIsImtpbmQiOiJ1c2VyIiwibmFtZSI6ImtvZmZpa291YWtvdWFsYSIsInJvbGVzIjpbImludGVudF9zdHVkZW50Il0sImVtYWlsIjoia29mZmlrb3Vha291YWxhaW5jb25zdGFudEBnbWFpbC5jb20iLCJ0aW1lX2NyZWF0ZWQiOjE3NDc5NDAyNDY0MjEsIl9tZXRhIjp7InByaXZhdGVBdHRyaWJ1dGVzIjpbImVtYWlsIl19fQ
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'ambient-light-sensor'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'battery'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'execution-while-not-rendered'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'execution-while-out-of-viewport'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'layout-animations'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'legacy-image-formats'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'navigation-override'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'oversized-images'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'publickey-credentials'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'speaker-selection'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'unoptimized-images'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'unsized-media'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Unrecognized feature: 'pointer-lock'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Allow attribute will take precedence over 'allowfullscreen'.
_ @ framework-a8e01971f0455ca9.js:1
framework-a8e01971f0455ca9.js:1 Allow attribute will take precedence over 'allowpaymentrequest'.
_ @ framework-a8e01971f0455ca9.js:1
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'ambient-light-sensor'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'battery'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'execution-while-not-rendered'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'execution-while-out-of-viewport'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'layout-animations'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'legacy-image-formats'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'navigation-override'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'oversized-images'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'publickey-credentials'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'speaker-selection'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'unoptimized-images'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Unrecognized feature: 'unsized-media'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Allow attribute will take precedence over 'allowfullscreen'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/workspace_iframe.html?initialPath=%2F&id=%3Ar71%3A:32 Allow attribute will take precedence over 'allowpaymentrequest'.
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/api/auth/signup:1 
            
            
           Failed to load resource: the server responded with a status of 400 (Bad Request)
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/api/auth/signup:1 
            
            
           Failed to load resource: the server responded with a status of 400 (Bad Request)
3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/api/auth/signup:1 
            
            
           Failed to load resource: the server responded with a status of 400 (Bad Request)
stallwart.build.js:1 stallwart: failed ping 1
stallwart.build.js:1 stallwart: failed ping 2
Console.js:61 Current location: /
_app-5f527e502df8030b.js:38 [LaunchDarkly] Closing stream connection
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 Current location: /contract/new
Console.js:61 Current location: /
Console.js:61 handleViewContracts called, isAuthenticated: true
Console.js:61 Navigating to /contracts
Console.js:61 Current location: /contracts
Console.js:61 Current location: /
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 Current location: /contract/new
Console.js:61 Current location: /
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 Current location: /contract/new
Console.js:61 Current location: /
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 Current location: /contract/new
Console.js:61 Current location: /
Console.js:61 handleCreateContract called, isAuthenticated: true
Console.js:61 Navigating to /contract/new
Console.js:61 Current location: /contract/new
Console.js:61 Current location: /
Console.js:61 handleViewContracts called, isAuthenticated: true
Console.js:61 Navigating to /contracts
Console.js:61 Current location: /contracts
Console.js:61 Current location: /

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
