import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { User, Mail, Phone, Building, Calendar, FileText, ArrowLeft, ExternalLink } from 'lucide-react';
import { formatDate } from '@/lib/utils';

// Types pour les mandants et les contrats
interface MandantDetail {
  id: string;
  name: string;
  email: string;
  mandantId: string;
  createdAt: string;
  phone: string;
  address: string;
  company: string;
  contracts: ContractSummary[];
}

interface ContractSummary {
  id: string;
  title: string;
  status: 'en_attente' | 'accepte' | 'rejete' | 'valide';
  createdAt: string;
  amount: number;
}

export function MandantDetailPage() {
  const [match, params] = useRoute('/mandant/:id');
  const [, setLocation] = useLocation();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { isAdmin } = useAdmin();
  
  const [mandant, setMandant] = useState<MandantDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');

  // Fonction pour charger les détails du mandant
  useEffect(() => {
    if (!isAuthenticated) {
      setLocation('/');
      return;
    }

    if (!isAdmin) {
      setLocation('/contracts');
      return;
    }

    if (!match || !params) {
      setLocation('/mandants');
      return;
    }

    const { id } = params;

    // Simuler le chargement des détails du mandant depuis une API
    const loadMandantDetails = async () => {
      setIsLoading(true);
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Données fictives pour la démonstration
        const mockMandant: MandantDetail = {
          id: '3',
          name: 'Cabinet Juridique Martin',
          email: '<EMAIL>',
          mandantId: id,
          createdAt: '2023-07-05T09:15:00Z',
          phone: '+33 1 23 45 67 89',
          address: '15 rue de la Paix, 75001 Paris, France',
          company: 'SAS Cabinet Martin & Associés',
          contracts: [
            {
              id: 'c1',
              title: 'Recouvrement créance Dupont',
              status: 'valide',
              createdAt: '2023-07-10T14:30:00Z',
              amount: 5000
            },
            {
              id: 'c2',
              title: 'Recouvrement facture impayée Société XYZ',
              status: 'en_attente',
              createdAt: '2023-08-15T10:45:00Z',
              amount: 12500
            },
            {
              id: 'c3',
              title: 'Recouvrement loyers impayés',
              status: 'accepte',
              createdAt: '2023-09-20T16:15:00Z',
              amount: 3200
            },
            {
              id: 'c4',
              title: 'Recouvrement facture Restaurant Le Gourmet',
              status: 'rejete',
              createdAt: '2023-10-05T11:30:00Z',
              amount: 1800
            },
            {
              id: 'c5',
              title: 'Recouvrement créance client particulier',
              status: 'valide',
              createdAt: '2023-11-12T09:20:00Z',
              amount: 2750
            }
          ]
        };
        
        setMandant(mockMandant);
      } catch (error) {
        console.error('Erreur lors du chargement des détails du mandant:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMandantDetails();
  }, [isAuthenticated, isAdmin, match, params, setLocation]);

  // Fonction pour obtenir la couleur en fonction du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valide':
        return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
      case 'en_attente':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100';
      case 'accepte':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100';
      case 'rejete':
        return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100';
    }
  };

  // Fonction pour obtenir le libellé du statut
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'valide':
        return 'Validé';
      case 'en_attente':
        return 'En attente';
      case 'accepte':
        return 'Accepté';
      case 'rejete':
        return 'Rejeté';
      default:
        return status;
    }
  };

  // Afficher un message de chargement
  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Rediriger si non authentifié ou non admin ou mandant non trouvé
  if (!isAuthenticated || !isAdmin || !mandant) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => setLocation('/mandants')}
            className="mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Détails du Mandant
          </h1>
          
          <div className="ml-4 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 rounded-full text-sm font-medium">
            {mandant.mandantId}
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="profile" className="flex items-center">
              <User className="h-4 w-4 mr-2" />
              Profil
            </TabsTrigger>
            <TabsTrigger value="contracts" className="flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              Contrats ({mandant.contracts.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="profile" className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Informations du mandant
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <User className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom</p>
                      <p className="text-base text-gray-900 dark:text-white">{mandant.name}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Mail className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                      <p className="text-base text-gray-900 dark:text-white">{mandant.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Phone className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone</p>
                      <p className="text-base text-gray-900 dark:text-white">{mandant.phone}</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Building className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Société</p>
                      <p className="text-base text-gray-900 dark:text-white">{mandant.company}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Date d'inscription</p>
                      <p className="text-base text-gray-900 dark:text-white">{formatDate(mandant.createdAt)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <svg className="h-5 w-5 text-gray-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse</p>
                      <p className="text-base text-gray-900 dark:text-white">{mandant.address}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Actions
              </h2>
              
              <div className="flex flex-wrap gap-3">
                <Button onClick={() => setLocation(`/mandant/${mandant.mandantId}/edit`)}>
                  Modifier les informations
                </Button>
                <Button variant="outline" onClick={() => setLocation(`/contract/new?mandant=${mandant.mandantId}`)}>
                  Créer un nouveau contrat
                </Button>
                <Button variant="destructive">
                  Désactiver le compte
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="contracts" className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Liste des contrats
                </h2>
                <Button onClick={() => setLocation(`/contract/new?mandant=${mandant.mandantId}`)}>
                  Nouveau contrat
                </Button>
              </div>
              
              {mandant.contracts.length === 0 ? (
                <div className="p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Aucun contrat</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    Ce mandant n'a pas encore de contrats.
                  </p>
                  <Button onClick={() => setLocation(`/contract/new?mandant=${mandant.mandantId}`)}>
                    Créer un contrat
                  </Button>
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Titre
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Montant
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Statut
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {mandant.contracts.map((contract) => (
                      <tr key={contract.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {contract.title}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {formatDate(contract.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {contract.amount.toLocaleString('fr-FR')} €
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(contract.status)}`}>
                            {getStatusLabel(contract.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setLocation(`/contract/${contract.id}`)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            Voir
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
