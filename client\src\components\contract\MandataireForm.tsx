import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MandataireData, User } from '@/types';
import { ArrowRight, UserPlus, Users } from 'lucide-react';

interface MandataireFormProps {
  onSubmit: (data: MandataireData, selectedMandantId?: string) => void;
  initialData?: MandataireData;
}

export function MandataireForm({ onSubmit, initialData }: MandataireFormProps) {
  const [formData, setFormData] = useState<MandataireData>(
    initialData || {
      nom: '',
      email: '',
      telephone: '',
      siret: '',
      adresse: '',
      creance: '',
      montant: 0,
      commission: 15,
    }
  );

  // État pour la liste des mandants
  const [mandants, setMandants] = useState<User[]>([]);
  const [selectedMandantId, setSelectedMandantId] = useState<string>('');
  const [showNewMandantForm, setShowNewMandantForm] = useState(false);
  const [newMandantEmail, setNewMandantEmail] = useState('');
  const [newMandantName, setNewMandantName] = useState('');

  // Charger la liste des mandants
  useEffect(() => {
    // Dans une vraie application, nous ferions un appel API
    // Ici, nous simulons un appel API avec des données locales
    const loadMandants = async () => {
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 500));

        // Données fictives pour la démonstration
        const mockMandants: User[] = [
          {
            id: 1,
            name: 'Entreprise ABC',
            email: '<EMAIL>',
            role: 'mandant',
            mandantId: 'ABCD1234'
          },
          {
            id: 2,
            name: 'Société XYZ',
            email: '<EMAIL>',
            role: 'mandant',
            mandantId: 'WXYZ5678'
          },
          {
            id: 3,
            name: 'Cabinet Juridique Martin',
            email: '<EMAIL>',
            role: 'mandant',
            mandantId: 'MART9012'
          }
        ];

        setMandants(mockMandants);
      } catch (error) {
        console.error('Erreur lors du chargement des mandants:', error);
      }
    };

    loadMandants();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedMandantId && !showNewMandantForm) {
      alert('Veuillez sélectionner un mandant existant ou créer un nouveau mandant.');
      return;
    }

    if (showNewMandantForm && (!newMandantEmail || !newMandantName)) {
      alert('Veuillez remplir tous les champs pour le nouveau mandant.');
      return;
    }

    // Si c'est un nouveau mandant, nous devrions normalement l'enregistrer d'abord
    // et obtenir son ID, mais ici nous simulons simplement
    if (showNewMandantForm) {
      // Dans une vraie application, nous enregistrerions le nouveau mandant
      // et utiliserions l'ID retourné
      const newMandantId = 'NEW' + Date.now().toString().substring(7);
      onSubmit(formData, newMandantId);
    } else {
      onSubmit(formData, selectedMandantId);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'montant' || name === 'commission' ? parseFloat(value) || 0 : value
    }));
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Informations du Mandataire</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="nom" className="block text-sm font-medium text-gray-700 mb-2">
              Nom complet / Dénomination sociale
            </Label>
            <Input
              type="text"
              name="nom"
              value={formData.nom}
              onChange={handleInputChange}
              required
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </Label>
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="telephone" className="block text-sm font-medium text-gray-700 mb-2">
              Téléphone
            </Label>
            <Input
              type="tel"
              name="telephone"
              value={formData.telephone}
              onChange={handleInputChange}
              required
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="siret" className="block text-sm font-medium text-gray-700 mb-2">
              SIRET (si applicable)
            </Label>
            <Input
              type="text"
              name="siret"
              value={formData.siret}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="adresse" className="block text-sm font-medium text-gray-700 mb-2">
            Adresse complète
          </Label>
          <Textarea
            name="adresse"
            value={formData.adresse}
            onChange={handleInputChange}
            required
            rows={3}
            className="w-full"
          />
        </div>

        <div>
          <Label htmlFor="creance" className="block text-sm font-medium text-gray-700 mb-2">
            Description de la créance
          </Label>
          <Textarea
            name="creance"
            value={formData.creance}
            onChange={handleInputChange}
            required
            rows={4}
            placeholder="Décrivez la nature de la créance à recouvrer..."
            className="w-full"
          />
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="montant" className="block text-sm font-medium text-gray-700 mb-2">
              Montant de la créance (€)
            </Label>
            <Input
              type="number"
              name="montant"
              value={formData.montant}
              onChange={handleInputChange}
              required
              step="0.01"
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="commission" className="block text-sm font-medium text-gray-700 mb-2">
              Commission (%)
            </Label>
            <Input
              type="number"
              name="commission"
              value={formData.commission}
              onChange={handleInputChange}
              step="0.1"
              className="w-full"
            />
          </div>
        </div>

        {/* Section Sélection du Mandant */}
        <div className="border-t border-gray-200 pt-6 mt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sélection du Mandant (Créancier)</h3>

          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Button
                type="button"
                variant={!showNewMandantForm ? "default" : "outline"}
                onClick={() => setShowNewMandantForm(false)}
                className="flex-1"
              >
                <Users className="mr-2 h-4 w-4" />
                Sélectionner un mandant existant
              </Button>

              <Button
                type="button"
                variant={showNewMandantForm ? "default" : "outline"}
                onClick={() => setShowNewMandantForm(true)}
                className="flex-1"
              >
                <UserPlus className="mr-2 h-4 w-4" />
                Créer un nouveau mandant
              </Button>
            </div>

            {!showNewMandantForm ? (
              <div className="space-y-4">
                <Label htmlFor="mandantSelect" className="block text-sm font-medium text-gray-700">
                  Sélectionnez un mandant dans la liste
                </Label>
                <Select
                  value={selectedMandantId}
                  onValueChange={setSelectedMandantId}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sélectionner un mandant" />
                  </SelectTrigger>
                  <SelectContent>
                    {mandants.map((mandant) => (
                      <SelectItem key={mandant.mandantId} value={mandant.mandantId || ''}>
                        {mandant.name} ({mandant.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedMandantId && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-700">
                      Le mandant sélectionné recevra un lien pour compléter ses informations et signer le contrat.
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="newMandantName" className="block text-sm font-medium text-gray-700 mb-2">
                    Nom du nouveau mandant
                  </Label>
                  <Input
                    id="newMandantName"
                    value={newMandantName}
                    onChange={(e) => setNewMandantName(e.target.value)}
                    placeholder="Nom complet ou dénomination sociale"
                    className="w-full"
                  />
                </div>

                <div>
                  <Label htmlFor="newMandantEmail" className="block text-sm font-medium text-gray-700 mb-2">
                    Email du nouveau mandant
                  </Label>
                  <Input
                    id="newMandantEmail"
                    type="email"
                    value={newMandantEmail}
                    onChange={(e) => setNewMandantEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full"
                  />
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-blue-700">
                    Un email sera envoyé au nouveau mandant avec un lien pour s'inscrire, compléter ses informations et signer le contrat.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            className="bg-primary text-white hover:bg-blue-700 px-8 py-3"
          >
            Continuer
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}
