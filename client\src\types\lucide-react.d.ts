declare module 'lucide-react' {
  import { FC, SVGProps } from 'react';
  
  export interface IconProps extends SVGProps<SVGSVGElement> {
    size?: string | number;
    color?: string;
    strokeWidth?: string | number;
  }

  export const FileSignature: FC<IconProps>;
  export const Share2: FC<IconProps>;
  export const TrendingUp: FC<IconProps>;
  export const ArrowLeft: FC<IconProps>;
  export const Building2: FC<IconProps>;
  export const Mail: FC<IconProps>;
  export const Lock: FC<IconProps>;
  export const User: FC<IconProps>;
} 