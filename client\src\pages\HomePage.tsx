import React, { useState, useEffect } from 'react';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { FileSignature, Share2, TrendingUp, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useLocation } from 'wouter';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { Building2, Mail, Lock, User as UserIcon } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { logLoginAttempt, updateLastLogin } from '@/lib/supabase';

export function HomePage() {
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  const { isAuthenticated, login, signup, isLoading: authLoading } = useAuth();
  const [, setLocation] = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [signupData, setSignupData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    country: '',
    city: '',
    neighborhood: '',
    profession: '',
    password: '',
    confirmPassword: ''
  });
  const [resetEmail, setResetEmail] = useState('');

  // Redirect authenticated users to their dashboard
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      setLocation('/mandants');
    }
  }, [isAuthenticated, authLoading, setLocation]);

  const handleCreateContract = () => {
    if (isAuthenticated) {
      setLocation('/contract/new');
    } else {
      setShowLoginModal(true);
    }
  };

  const handleViewContracts = () => {
    if (isAuthenticated) {
      setLocation('/contracts');
    } else {
      setShowLoginModal(true);
    }
  };

  const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setLoginData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSignupChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSignupData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  const validatePhone = (phone: string) => /^\+?[\d\s-]{7,}$/.test(phone);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Vérifier si l'utilisateur existe
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('email', loginData.email)
        .single();

      if (userError || !userData) {
        throw new Error('Aucun compte trouvé avec cette adresse email.');
      }

      if (!userData.is_active) {
        throw new Error('Ce compte a été désactivé. Veuillez contacter l\'administrateur.');
      }

      // Connexion
      const success = await login(loginData.email, loginData.password);

      if (success) {
        // Mettre à jour la dernière connexion et enregistrer la tentative
        await updateLastLogin(userData.id);
        await logLoginAttempt(
          userData.id,
          loginData.email,
          true,
          window.navigator.userAgent
        );

        toast({
          title: "Connexion réussie",
          description: "Vous êtes maintenant connecté.",
        });

        setShowLoginModal(false);
      }
    } catch (error: any) {
      console.error('Erreur lors de la connexion:', error);

      let errorMessage = "Une erreur est survenue lors de la connexion.";

      if (error.message) {
        errorMessage = error.message;
      }

      // Enregistrer la tentative échouée
      await logLoginAttempt(
        null,
        loginData.email,
        false,
        window.navigator.userAgent
      );

      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Vérifier que les mots de passe correspondent
      if (signupData.password !== signupData.confirmPassword) {
        throw new Error('Les mots de passe ne correspondent pas.');
      }

      // Vérifier la longueur du mot de passe
      if (signupData.password.length < 6) {
        throw new Error('Le mot de passe doit contenir au moins 6 caractères.');
      }

      // Créer l'utilisateur directement avec Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email: signupData.email,
        password: signupData.password,
      });

      if (error) {
        // Gérer les erreurs spécifiques de Supabase
        if (error.message.includes('already registered')) {
          throw new Error('Un compte existe déjà avec cette adresse email.');
        }
        throw new Error(error.message);
      }

      if (data.user) {
        // Créer l'entrée dans la table users
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: signupData.email,
            first_name: signupData.firstName,
            last_name: signupData.lastName,
            phone: signupData.phone,
            country: signupData.country,
            city: signupData.city,
            neighborhood: signupData.neighborhood,
            profession: signupData.profession,
            role: 'mandant',
            is_active: true,
          });

        if (insertError) {
          console.error('Erreur lors de la création du profil:', insertError);
          // Ne pas bloquer l'inscription si l'insertion échoue
        }

        toast({
          title: "Compte créé",
          description: "Votre compte a été créé avec succès. Veuillez vérifier votre email pour activer votre compte.",
        });

        setShowSignupModal(false);
        setLocation('/');
      }
    } catch (error: any) {
      console.error('Erreur lors de l\'inscription:', error);

      let errorMessage = "Une erreur est survenue lors de l'inscription.";

      if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (!validateEmail(resetEmail)) {
        throw new Error('Adresse email invalide.');
      }

      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;

      toast({
        title: "Email envoyé",
        description: "Les instructions de réinitialisation ont été envoyées à votre adresse email.",
      });
      setShowResetPasswordModal(false);
    } catch (error: any) {
      console.error('Erreur lors de l\'envoi de l\'email:', error);
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de l'envoi de l'email.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation
        onLoginClick={() => setShowLoginModal(true)}
        onSignupClick={() => setShowSignupModal(true)}
      />

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary to-blue-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Bienvenue sur RecovExpert
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Plateforme professionnelle de gestion de contrats de recouvrement amiable
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleCreateContract}
                className="bg-white text-primary px-8 py-3 hover:bg-gray-100 font-semibold"
                disabled={isLoading || authLoading}
              >
                Créer un nouveau contrat
              </Button>
              <Button
                onClick={handleViewContracts}
                variant="outline"
                className="bg-transparent border-2 border-white text-white px-8 py-3 hover:bg-white hover:text-primary font-semibold"
                disabled={isLoading || authLoading}
              >
                Voir mes contrats
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Gestion simplifiée de vos contrats
            </h2>
            <p className="text-xl text-gray-600">
              Une solution complète pour automatiser vos processus de recouvrement
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="bg-primary/10 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileSignature className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Création rapide</h3>
              <p className="text-gray-600">Créez et partagez vos contrats en quelques clics</p>
            </div>
            <div className="text-center p-6">
              <div className="bg-green-600/10 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Share2 className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Partage instantané</h3>
              <p className="text-gray-600">Envoyez par WhatsApp ou email automatiquement</p>
            </div>
            <div className="text-center p-6">
              <div className="bg-orange-500/10 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Suivi en temps réel</h3>
              <p className="text-gray-600">Suivez le statut de tous vos contrats</p>
            </div>
          </div>
        </div>
      </div>

      {/* Login Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Connexion
              </CardTitle>
              <CardDescription>
                Connectez-vous à votre compte
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={loginData.email}
                    onChange={handleLoginChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Mot de passe
                  </Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={loginData.password}
                    onChange={handleLoginChange}
                    required
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="rememberMe"
                      name="rememberMe"
                      checked={loginData.rememberMe}
                      onChange={handleLoginChange}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="rememberMe">Se souvenir de moi</Label>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setShowLoginModal(false);
                      setShowResetPasswordModal(true);
                    }}
                    className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Mot de passe oublié ?
                  </button>
                </div>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => setShowLoginModal(false)}
                  >
                    Annuler
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isLoading || authLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                        </svg>
                        Connexion...
                      </span>
                    ) : (
                      'Se connecter'
                    )}
                  </Button>
                </div>
              </form>

              <div className="mt-4 text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Pas encore de compte ?{' '}
                  <button
                    onClick={() => {
                      setShowLoginModal(false);
                      setShowSignupModal(true);
                    }}
                    className="text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    S'inscrire
                  </button>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Reset Password Modal */}
      {showResetPasswordModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Réinitialiser le mot de passe
              </CardTitle>
              <CardDescription>
                Entrez votre email pour recevoir les instructions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleResetPassword} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="resetEmail" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email
                  </Label>
                  <Input
                    id="resetEmail"
                    type="email"
                    value={resetEmail}
                    onChange={(e) => setResetEmail(e.target.value)}
                    required
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      setShowResetPasswordModal(false);
                      setShowLoginModal(true);
                    }}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Retour
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isLoading || authLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                        </svg>
                        Envoi...
                      </span>
                    ) : (
                      'Envoyer'
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Signup Modal */}
      {showSignupModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <Card className="w-full max-w-2xl mx-auto my-8">
            <CardHeader className="sticky top-0 bg-white dark:bg-gray-900 z-10 border-b">
              <CardTitle className="flex 청소기items-center gap-2">
                <Building2 className="h-5 w-5" />
                Inscription
              </CardTitle>
              <CardDescription>
                Créez votre compte
              </CardDescription>
            </CardHeader>
            <CardContent className="max-h-[calc(100vh-200px)] overflow-y-auto">
              <form onSubmit={handleSignup} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4" />
                      Prénom
                    </Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={signupData.firstName}
                      onChange={handleSignupChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4" />
                      Nom
                    </Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={signupData.lastName}
                      onChange={handleSignupChange}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={signupData.email}
                    onChange={handleSignupChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4" />
                    Téléphone
                  </Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={signupData.phone}
                    onChange={handleSignupChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country" className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4" />
                    Pays actuel
                  </Label>
                  <Input
                    id="country"
                    name="country"
                    value={signupData.country}
                    onChange={handleSignupChange}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city" className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4" />
                      Ville/Village
                    </Label>
                    <Input
                      id="city"
                      name="city"
                      value={signupData.city}
                      onChange={handleSignupChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="neighborhood" className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4" />
                      Quartier
                    </Label>
                    <Input
                      id="neighborhood"
                      name="neighborhood"
                      value={signupData.neighborhood}
                      onChange={handleSignupChange}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="profession" className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4" />
                    Profession
                  </Label>
                  <Input
                    id="profession"
                    name="profession"
                    value={signupData.profession}
                    onChange={handleSignupChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Mot de passe
                  </Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={signupData.password}
                    onChange={handleSignupChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Confirmer le mot de passe
                  </Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={signupData.confirmPassword}
                    onChange={handleSignupChange}
                    required
                  />
                </div>

                <div className="sticky bottom-0 bg-white dark:bg-gray-900 pt-4 border-t">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="flex-1"
                      onClick={() => setShowSignupModal(false)}
                    >
                      Annuler
                    </Button>
                    <Button
                      type="submit"
                      className="flex-1"
                      disabled={isLoading || authLoading}
                    >
                      {isLoading ? (
                        <span className="flex items-center">
                          <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          </svg>
                          Inscription...
                        </span>
                      ) : (
                        'S\'inscrire'
                      )}
                    </Button>
                  </div>

                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Déjà un compte ?{' '}
                      <button
                        onClick={() => {
                          setShowSignupModal(false);
                          setShowLoginModal(true);
                        }}
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        Se connecter
                      </button>
                    </p>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}