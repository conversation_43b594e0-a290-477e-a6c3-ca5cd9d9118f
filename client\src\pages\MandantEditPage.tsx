import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { ArrowLeft, Save } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface MandantFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  company: string;
  siret?: string;
}

export function MandantEditPage() {
  const [match, params] = useRoute('/mandant/:id/edit');
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { isAdmin } = useAdmin();

  const [formData, setFormData] = useState<MandantFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    company: '',
    siret: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      setLocation('/');
      return;
    }

    if (!isAdmin) {
      setLocation('/contracts');
      return;
    }

    if (match && params) {
      loadMandant(params.id);
    }
  }, [isAuthenticated, isAdmin, match, params, setLocation]);

  const loadMandant = async (mandantId: string) => {
    try {
      const response = await apiRequest('GET', `/api/mandants/${mandantId}`);
      const mandant = await response.json();
      setFormData({
        name: mandant.name,
        email: mandant.email,
        phone: mandant.phone,
        address: mandant.address,
        company: mandant.company,
        siret: mandant.siret
      });
    } catch (error) {
      console.error('Erreur lors du chargement du mandant:', error);
      alert('Une erreur est survenue lors du chargement du mandant.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      await apiRequest('PUT', `/api/mandants/${params?.id}`, formData);
      setLocation(`/mandant/${params?.id}`);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du mandant:', error);
      alert('Une erreur est survenue lors de la sauvegarde du mandant.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => setLocation(`/mandant/${params?.id}`)}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Modifier le mandant</h1>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Nom complet / Dénomination sociale
                </Label>
                <Input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full"
                />
              </div>

              <div>
                <Label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email
                </Label>
                <Input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full"
                />
              </div>

              <div>
                <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Téléphone
                </Label>
                <Input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                  className="w-full"
                />
              </div>

              <div>
                <Label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Entreprise
                </Label>
                <Input
                  type="text"
                  id="company"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className="w-full"
                />
              </div>

              <div>
                <Label htmlFor="siret" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  SIRET (si applicable)
                </Label>
                <Input
                  type="text"
                  id="siret"
                  name="siret"
                  value={formData.siret}
                  onChange={handleInputChange}
                  className="w-full"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Adresse complète
              </Label>
              <Textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full"
              />
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isSaving}
                className="bg-primary text-white hover:bg-blue-700 px-8 py-3"
              >
                {isSaving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Enregistrer
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 