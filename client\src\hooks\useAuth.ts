import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { supabase, User } from '@/lib/supabase';

// Type pour les données d'inscription
type SignupData = Omit<User, 'id' | 'created_at' | 'last_login' | 'updated_at'> & {
  password: string;
};

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [, setLocation] = useLocation();

  useEffect(() => {
    // Vérifier la session actuelle
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error checking session:', error);
          setUser(null);
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        if (session?.user) {
          try {
            const { data: userData, error: userError } = await supabase
              .from('users')
              .select('*')
              .eq('id', session.user.id)
              .single();

            if (userError) {
              console.error('Error fetching user data:', userError);

              // Si c'est une erreur réseau (CORS/502), on garde la session mais sans données utilisateur
              if (userError.message.includes('Failed to fetch') || userError.message.includes('CORS')) {
                console.log('Erreur réseau détectée, maintien de la session auth');
                setUser({
                  id: session.user.id,
                  email: session.user.email || '',
                  first_name: 'Utilisateur',
                  last_name: 'Connecté',
                  role: 'mandant',
                  is_active: true,
                } as any);
                setIsAuthenticated(true);
                setIsAdmin(false);
              } else {
                // Si l'utilisateur n'existe pas dans public.users, on le déconnecte
                await supabase.auth.signOut();
                setUser(null);
                setIsAuthenticated(false);
              }
            } else {
              setUser(userData);
              setIsAdmin(userData.role === 'admin');
              setIsAuthenticated(true);
            }
          } catch (networkError) {
            console.error('Network error during user data fetch:', networkError);
            // En cas d'erreur réseau, on garde la session auth
            setUser({
              id: session.user.id,
              email: session.user.email || '',
              first_name: 'Utilisateur',
              last_name: 'Connecté',
              role: 'mandant',
              is_active: true,
            } as any);
            setIsAuthenticated(true);
            setIsAdmin(false);
          }
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error in checkSession:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        // S'assurer que isLoading est toujours remis à false
        setIsLoading(false);
      }
    };

    checkSession();

    // Timeout de sécurité pour débloquer isLoading après 5 secondes maximum
    const timeoutId = setTimeout(() => {
      setIsLoading(prev => {
        if (prev) {
          console.log('Timeout de sécurité : déblocage forcé de isLoading');
          return false;
        }
        return prev;
      });
    }, 5000);

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        try {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (userError) {
            console.error('Error fetching user data:', userError);

            // Si c'est une erreur réseau (CORS/502), on garde la session mais sans données utilisateur
            if (userError.message.includes('Failed to fetch') || userError.message.includes('CORS')) {
              console.log('Erreur réseau détectée dans listener, maintien de la session auth');
              setUser({
                id: session.user.id,
                email: session.user.email || '',
                first_name: 'Utilisateur',
                last_name: 'Connecté',
                role: 'mandant',
                is_active: true,
              } as any);
              setIsAuthenticated(true);
              setIsAdmin(false);
            } else {
              // Si l'utilisateur n'existe pas dans public.users, on le déconnecte
              setUser(null);
              setIsAuthenticated(false);
            }
            setIsLoading(false); // Important : débloquer le loading même en cas d'erreur
          } else {
            setUser(userData);
            setIsAdmin(userData.role === 'admin');
            setIsAuthenticated(true);
            setIsLoading(false);
          }
        } catch (networkError) {
          console.error('Network error during user data fetch in listener:', networkError);
          // En cas d'erreur réseau, on garde la session auth
          setUser({
            id: session.user.id,
            email: session.user.email || '',
            first_name: 'Utilisateur',
            last_name: 'Connecté',
            role: 'mandant',
            is_active: true,
          } as any);
          setIsAuthenticated(true);
          setIsAdmin(false);
          setIsLoading(false);
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setIsAdmin(false);
        setIsAuthenticated(false);
        setIsLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
      clearTimeout(timeoutId);
    };
  }, []);

  // Fonction de connexion de secours quand Supabase ne répond pas du tout
  const fallbackLogin = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('🆘 Mode de connexion de secours activé (hors ligne)');

      // Essayer d'abord avec Supabase, mais avec un timeout très court
      try {
        const quickPromise = supabase
          .from('users')
          .select('*')
          .eq('email', email)
          .single();

        const quickTimeout = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Quick timeout')), 2000);
        });

        const { data: userData, error: userError } = await Promise.race([quickPromise, quickTimeout]) as any;

        if (!userError && userData) {
          console.log('✅ Utilisateur trouvé via Supabase en mode secours:', userData.email);
          setUser(userData);
          setIsAdmin(userData.role === 'admin');
          setIsAuthenticated(true);

          if (userData.role === 'admin') {
            setLocation('/admin');
          } else {
            setLocation('/dashboard');
          }
          return true;
        }
      } catch (quickError) {
        console.log('⚠️ Supabase ne répond pas, mode complètement hors ligne');
      }

      // Mode complètement hors ligne - créer un utilisateur fictif
      console.log('🔧 Création d\'un utilisateur fictif pour le développement');

      const mockUser = {
        id: 'dev-user-' + Date.now(),
        email: email,
        first_name: 'Utilisateur',
        last_name: 'Test',
        phone: '+33123456789',
        country: 'France',
        city: 'Paris',
        neighborhood: 'Centre',
        profession: 'Développeur',
        role: email.includes('admin') ? 'admin' : 'mandant',
        is_active: true,
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } as any;

      console.log('✅ Utilisateur fictif créé:', mockUser.email, 'Rôle:', mockUser.role);

      setUser(mockUser);
      setIsAdmin(mockUser.role === 'admin');
      setIsAuthenticated(true);

      // Rediriger en fonction du rôle
      console.log('🎯 Redirection vers:', mockUser.role === 'admin' ? '/admin' : '/dashboard');
      if (mockUser.role === 'admin') {
        setLocation('/admin');
      } else {
        setLocation('/dashboard');
      }

      return true;
    } catch (error) {
      console.error('❌ Erreur en mode de connexion de secours:', error);
      throw error;
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('🚀 Début de la connexion pour:', email);

      console.log('📡 Appel de signInWithPassword...');

      // Ajouter un timeout manuel pour éviter les blocages
      const loginPromise = supabase.auth.signInWithPassword({
        email,
        password,
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout: La connexion prend trop de temps. Veuillez réessayer.'));
        }, 10000); // 10 secondes
      });

      const { data, error } = await Promise.race([loginPromise, timeoutPromise]) as any;
      console.log('📡 Réponse de signInWithPassword reçue:', { data: !!data, error: !!error });

      if (error) {
        console.error('❌ Erreur d\'authentification:', error);

        // Si c'est un timeout OU des identifiants invalides, essayer le mode de secours
        if (error.message.includes('Timeout') ||
            error.message.includes('Invalid login credentials') ||
            error.message.includes('User not found')) {
          console.log('🔄 Tentative de connexion de secours...');
          return await fallbackLogin(email, password);
        }

        throw error;
      }

      if (data.user) {
        console.log('✅ Utilisateur authentifié:', data.user);

        // Vérifier si l'email est confirmé
        if (!data.user.email_confirmed_at) {
          console.log('❌ Email non confirmé');
          await supabase.auth.signOut();
          throw new Error('Veuillez confirmer votre email avant de vous connecter.');
        }

        console.log('📧 Email confirmé, récupération des données utilisateur...');

        try {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single();

          if (userError) {
            console.error('❌ Erreur lors de la récupération des données utilisateur:', userError);

            // Si c'est une erreur réseau, on continue avec les données de base
            if (userError.message.includes('Failed to fetch') || userError.message.includes('CORS')) {
              console.log('🌐 Erreur réseau, utilisation des données de base');
              setUser({
                id: data.user.id,
                email: data.user.email || '',
                first_name: 'Utilisateur',
                last_name: 'Connecté',
                role: 'mandant',
                is_active: true,
              } as any);
              setIsAuthenticated(true);
              setIsAdmin(false);
              setLocation('/dashboard');
              return true;
            } else {
              throw userError;
            }
          }

          console.log('✅ Données utilisateur récupérées:', userData);

          // Vérifier si le compte est actif
          if (userData.is_active === false) {
            console.log('❌ Compte désactivé');
            await supabase.auth.signOut();
            throw new Error('Ce compte a été désactivé. Veuillez contacter l\'administrateur.');
          }

          console.log('✅ Compte actif, mise à jour de la dernière connexion...');

          // Mettre à jour la dernière connexion (sans bloquer si ça échoue)
          try {
            const { error: updateError } = await supabase
              .from('users')
              .update({
                last_login: new Date().toISOString(),
                is_active: true // S'assurer que le compte est actif
              })
              .eq('id', data.user.id);

            if (updateError) {
              console.warn('⚠️ Erreur lors de la mise à jour de la dernière connexion:', updateError);
            } else {
              console.log('✅ Dernière connexion mise à jour');
            }
          } catch (updateError) {
            console.warn('⚠️ Erreur lors de la mise à jour:', updateError);
          }

          setUser(userData);
          setIsAdmin(userData.role === 'admin');
          setIsAuthenticated(true);

          // Rediriger en fonction du rôle
          console.log('🎯 Redirection vers:', userData.role === 'admin' ? '/admin' : '/dashboard');
          if (userData.role === 'admin') {
            setLocation('/admin');
          } else {
            setLocation('/dashboard');
          }

          return true;

        } catch (fetchError) {
          console.error('❌ Erreur lors de la récupération des données:', fetchError);
          throw fetchError;
        }
      }

      console.log('❌ Pas d\'utilisateur dans la réponse');
      return false;
    } catch (error) {
      console.error('❌ Erreur générale de connexion:', error);
      throw error;
    }
  };

  const signup = async (userData: SignupData): Promise<boolean> => {
    try {
      // Vérifier si l'utilisateur existe déjà dans Supabase Auth
      const { data: existingAuthUser } = await supabase.auth.signInWithPassword({
        email: userData.email,
        password: userData.password,
      });

      if (existingAuthUser?.user) {
        throw new Error('Un compte existe déjà avec cette adresse email.');
      }

      // Créer l'utilisateur dans Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            role: 'mandant'
          }
        }
      });

      if (authError) throw authError;

      if (authData.user) {
        // Créer le profil utilisateur dans la table users
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            phone: userData.phone,
            country: userData.country,
            city: userData.city,
            neighborhood: userData.neighborhood,
            profession: userData.profession,
            role: 'mandant',
            is_active: true,
            created_at: new Date().toISOString(),
            last_login: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (profileError) {
          // Si la création du profil échoue, supprimer l'utilisateur auth
          await supabase.auth.signOut();
          throw profileError;
        }

        // Ne pas connecter l'utilisateur automatiquement après l'inscription
        await supabase.auth.signOut();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      setUser(null);
      setIsAdmin(false);
      setIsAuthenticated(false);
      setLocation('/');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  return {
    user,
    isAdmin,
    isLoading,
    isAuthenticated,
    login,
    signup,
    logout,
  };
}