import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { supabase, User } from '@/lib/supabase';

// Type pour les données d'inscription
type SignupData = Omit<User, 'id' | 'created_at' | 'last_login' | 'updated_at'> & {
  password: string;
};

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [, setLocation] = useLocation();

  useEffect(() => {
    // Vérifier la session actuelle
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error checking session:', error);
          setUser(null);
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        if (session?.user) {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (userError) {
            console.error('Error fetching user data:', userError);
            // Si l'utilisateur n'existe pas dans public.users, on le déconnecte
            await supabase.auth.signOut();
            setUser(null);
            setIsAuthenticated(false);
          } else {
            setUser(userData);
            setIsAdmin(userData.role === 'admin');
            setIsAuthenticated(true);
          }
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error in checkSession:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        // S'assurer que isLoading est toujours remis à false
        setIsLoading(false);
      }
    };

    checkSession();

    // Timeout de sécurité pour débloquer isLoading après 5 secondes maximum
    const timeoutId = setTimeout(() => {
      console.log('Timeout de sécurité : déblocage de isLoading');
      setIsLoading(false);
    }, 5000);

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (userError) {
          console.error('Error fetching user data:', userError);
          setUser(null);
          setIsAuthenticated(false);
          setIsLoading(false); // Important : débloquer le loading même en cas d'erreur
        } else {
          setUser(userData);
          setIsAdmin(userData.role === 'admin');
          setIsAuthenticated(true);
          setIsLoading(false);
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setIsAdmin(false);
        setIsAuthenticated(false);
        setIsLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
      clearTimeout(timeoutId);
    };
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('Tentative de connexion pour:', email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Erreur d\'authentification:', error);
        throw error;
      }

      if (data.user) {
        console.log('Utilisateur authentifié:', data.user);

        // Vérifier si l'email est confirmé
        if (!data.user.email_confirmed_at) {
          console.log('Email non confirmé');
          await supabase.auth.signOut();
          throw new Error('Veuillez confirmer votre email avant de vous connecter.');
        }

        console.log('Email confirmé, récupération des données utilisateur...');
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (userError) {
          console.error('Erreur lors de la récupération des données utilisateur:', userError);
          throw userError;
        }

        console.log('Données utilisateur récupérées:', userData);

        // Vérifier si le compte est actif
        if (userData.is_active === false) {
          console.log('Compte désactivé');
          await supabase.auth.signOut();
          throw new Error('Ce compte a été désactivé. Veuillez contacter l\'administrateur.');
        }

        console.log('Compte actif, mise à jour de la dernière connexion...');
        // Mettre à jour la dernière connexion
        const { error: updateError } = await supabase
          .from('users')
          .update({
            last_login: new Date().toISOString(),
            is_active: true // S'assurer que le compte est actif
          })
          .eq('id', data.user.id);

        if (updateError) {
          console.error('Erreur lors de la mise à jour de la dernière connexion:', updateError);
        }

        setUser(userData);
        setIsAdmin(userData.role === 'admin');
        setIsAuthenticated(true);

        // Rediriger en fonction du rôle
        if (userData.role === 'admin') {
          setLocation('/admin');
        } else {
          setLocation('/mandants');
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const signup = async (userData: SignupData): Promise<boolean> => {
    try {
      // Vérifier si l'utilisateur existe déjà dans Supabase Auth
      const { data: existingAuthUser } = await supabase.auth.signInWithPassword({
        email: userData.email,
        password: userData.password,
      });

      if (existingAuthUser?.user) {
        throw new Error('Un compte existe déjà avec cette adresse email.');
      }

      // Créer l'utilisateur dans Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            role: 'mandant'
          }
        }
      });

      if (authError) throw authError;

      if (authData.user) {
        // Créer le profil utilisateur dans la table users
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            phone: userData.phone,
            country: userData.country,
            city: userData.city,
            neighborhood: userData.neighborhood,
            profession: userData.profession,
            role: 'mandant',
            is_active: true,
            created_at: new Date().toISOString(),
            last_login: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (profileError) {
          // Si la création du profil échoue, supprimer l'utilisateur auth
          await supabase.auth.signOut();
          throw profileError;
        }

        // Ne pas connecter l'utilisateur automatiquement après l'inscription
        await supabase.auth.signOut();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      setUser(null);
      setIsAdmin(false);
      setIsAuthenticated(false);
      setLocation('/');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  return {
    user,
    isAdmin,
    isLoading,
    isAuthenticated,
    login,
    signup,
    logout,
  };
}