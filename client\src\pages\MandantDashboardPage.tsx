import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/useAuth';
import { FileText, Clock, CheckCircle, AlertCircle, XCircle, BarChart, ArrowRight, Download, Printer } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { Contract } from '@/types';

export function MandantDashboardPage() {
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();

  const [contracts, setContracts] = useState<Contract[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  // Charger les contrats du mandant
  useEffect(() => {
    if (!isAuthenticated) {
      setLocation('/');
      return;
    }

    // Si l'utilisateur est un admin, le rediriger vers la page d'administration
    if (user?.role === 'admin') {
      setLocation('/admin');
      return;
    }

    // Simuler le chargement des contrats depuis une API
    const loadContracts = async () => {
      setIsLoading(true);
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Vérifier si c'est un nouvel utilisateur (première connexion)
        const isNewUser = localStorage.getItem(`user_${user?.id}_first_login`) === null;

        if (isNewUser) {
          // Marquer que l'utilisateur s'est connecté au moins une fois
          localStorage.setItem(`user_${user?.id}_first_login`, 'false');

          // Pour un nouvel utilisateur, retourner une liste vide
          setContracts([]);
        } else {
          // Pour un utilisateur existant, charger ses contrats
          // Dans une vraie application, ces données viendraient de l'API
          // et seraient filtrées par l'ID du mandant

          // Simuler un cas où l'utilisateur a des contrats (pour la démo)
          // Dans une vraie application, cette logique serait côté serveur
          const userHasContracts = Math.random() > 0.5; // 50% de chance d'avoir des contrats

          if (userHasContracts) {
            // Données fictives pour la démonstration
            const mockContracts: Contract[] = [
              {
                id: 'c1',
                status: 'valide',
                createdAt: '2023-07-10T14:30:00Z',
                signedAt: '2023-07-15T09:45:00Z',
                mandataireData: {
                  nom: 'RecovExpert',
                  email: '<EMAIL>',
                  telephone: '+33 1 23 45 67 89',
                  adresse: '123 Avenue de la République, 75011 Paris',
                  creance: 'Facture impayée n°F2023-156',
                  montant: 5000,
                  commission: 15
                },
                mandantData: {
                  nom: user?.name || 'Client',
                  email: user?.email || '<EMAIL>',
                  telephone: '+33 6 12 34 56 78',
                  adresse: '45 Rue du Commerce, 75015 Paris'
                },
                userId: user?.id // Associer le contrat à l'utilisateur actuel
              },
              {
                id: 'c2',
                status: 'en_attente',
                createdAt: '2023-08-15T10:45:00Z',
                mandataireData: {
                  nom: 'RecovExpert',
                  email: '<EMAIL>',
                  telephone: '+33 1 23 45 67 89',
                  adresse: '123 Avenue de la République, 75011 Paris',
                  creance: 'Loyers impayés (3 mois)',
                  montant: 3600,
                  commission: 12
                },
                mandantData: {
                  nom: user?.name || 'Client',
                  email: user?.email || '<EMAIL>',
                  telephone: '+33 6 12 34 56 78',
                  adresse: '45 Rue du Commerce, 75015 Paris'
                },
                userId: user?.id // Associer le contrat à l'utilisateur actuel
              }
            ];

            setContracts(mockContracts);
          } else {
            // L'utilisateur n'a pas encore de contrats
            setContracts([]);
          }
        }
      } catch (error) {
        console.error('Erreur lors du chargement des contrats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContracts();
  }, [isAuthenticated, user, setLocation]);

  // Filtrer les contrats en fonction de l'onglet actif
  const filteredContracts = contracts.filter(contract => {
    if (activeTab === 'all') return true;
    return contract.status === activeTab;
  });

  // Obtenir les statistiques des contrats
  const getContractStats = () => {
    const total = contracts.length;
    const validated = contracts.filter(c => c.status === 'valide').length;
    const pending = contracts.filter(c => c.status === 'en_attente').length;
    const accepted = contracts.filter(c => c.status === 'accepte').length;
    const rejected = contracts.filter(c => c.status === 'rejete').length;

    const totalAmount = contracts
      .filter(c => c.status === 'valide' || c.status === 'accepte')
      .reduce((sum, contract) => sum + (contract.mandataireData?.montant || 0), 0);

    return { total, validated, pending, accepted, rejected, totalAmount };
  };

  const stats = getContractStats();

  // Fonction pour obtenir la couleur en fonction du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valide':
        return 'text-green-600 dark:text-green-400';
      case 'en_attente':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'accepte':
        return 'text-blue-600 dark:text-blue-400';
      case 'rejete':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  // Fonction pour obtenir l'icône en fonction du statut
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valide':
        return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />;
      case 'en_attente':
        return <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />;
      case 'accepte':
        return <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />;
      case 'rejete':
        return <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  // Fonction pour obtenir le libellé du statut
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'valide':
        return 'Validé';
      case 'en_attente':
        return 'En attente';
      case 'accepte':
        return 'Accepté';
      case 'rejete':
        return 'Rejeté';
      default:
        return status;
    }
  };

  // Afficher un message de chargement
  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Rediriger si non authentifié
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Tableau de bord</h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Bienvenue, {user?.name}. Gérez vos contrats de recouvrement.
            </p>
          </div>

          <Button
            onClick={() => setLocation('/contracts')}
            className="bg-primary text-white"
          >
            Voir mes contrats en cours
          </Button>
        </div>

        {/* Statistiques */}
        {contracts.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mb-8 text-center">
            <div className="max-w-md mx-auto">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Bienvenue sur votre tableau de bord</h2>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Vous n'avez pas encore de contrats de recouvrement en cours. Veuillez attendre qu'un mandataire crée un contrat pour vous ou contactez RecovExpert.
              </p>
              <Button
                onClick={() => setLocation('/contracts')}
                className="bg-primary text-white px-6 py-2"
              >
                Vérifier mes contrats en cours
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  Contrats
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{stats.total}</div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Total des contrats</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-green-600 dark:text-green-400" />
                  Validés
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{stats.validated}</div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Contrats validés</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Clock className="mr-2 h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                  En attente
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{stats.pending}</div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Contrats en attente</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <BarChart className="mr-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Montant total
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{stats.totalAmount.toLocaleString('fr-FR')} €</div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Contrats validés et acceptés</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Liste des contrats */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Mes contrats</CardTitle>
            <CardDescription>
              Gérez vos contrats de recouvrement
            </CardDescription>

            {contracts.length > 0 ? (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
                <TabsList>
                  <TabsTrigger value="all">Tous ({stats.total})</TabsTrigger>
                  <TabsTrigger value="valide">Validés ({stats.validated})</TabsTrigger>
                  <TabsTrigger value="en_attente">En attente ({stats.pending})</TabsTrigger>
                  <TabsTrigger value="accepte">Acceptés ({stats.accepted})</TabsTrigger>
                  <TabsTrigger value="rejete">Rejetés ({stats.rejected})</TabsTrigger>
                </TabsList>
              </Tabs>
            ) : null}
          </CardHeader>

          <CardContent>
            {filteredContracts.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-6" />
                <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-3">Aucun contrat</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                  {contracts.length === 0
                    ? "Vous n'avez pas encore de contrats en cours. Veuillez attendre qu'un mandataire crée un contrat pour vous."
                    : "Vous n'avez pas encore de contrats dans cette catégorie."}
                </p>
                <Button
                  onClick={() => setLocation('/contracts')}
                  className="bg-primary text-white px-6 py-2"
                >
                  Vérifier mes contrats
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredContracts.map((contract) => (
                  <div
                    key={contract.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-4">
                        {getStatusIcon(contract.status)}
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {contract.mandataireData?.creance || 'Contrat de recouvrement'}
                          </h3>
                          <div className="flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400">
                            <span className="mr-3">Montant: {contract.mandataireData?.montant.toLocaleString('fr-FR')} €</span>
                            <span className="mr-3">Commission: {contract.mandataireData?.commission}%</span>
                            <span>Créé le: {formatDate(contract.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(contract.status)} bg-opacity-10 dark:bg-opacity-20 mr-4`}>
                          {getStatusLabel(contract.status)}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setLocation(`/contract/${contract.id}`)}
                        >
                          Voir
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {contract.status === 'valide' && (
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
                        <Button variant="outline" size="sm">
                          <Printer className="mr-2 h-4 w-4" />
                          Imprimer
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Télécharger PDF
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Affichage de {filteredContracts.length} contrat{filteredContracts.length !== 1 ? 's' : ''}
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
