import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';

export function useAdmin() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!authLoading && isAuthenticated && user) {
      // Vérifier le rôle directement depuis les données de l'utilisateur
      setIsAdmin(user.role === 'admin');
      setIsLoading(false);
    } else if (!authLoading) {
      setIsAdmin(false);
      setIsLoading(false);
    }
  }, [user, isAuthenticated, authLoading]);

  return {
    isAdmin,
    isLoading: isLoading || authLoading
  };
}