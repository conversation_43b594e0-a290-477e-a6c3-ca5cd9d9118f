import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateContractId(): string {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `CONTRACT-${timestamp}-${random}`;
}

/**
 * Génère un ID unique pour un mandant au format XXXX0000
 * Les 4 premiers caractères sont des lettres majuscules aléatoires
 * Les 4 derniers caractères sont des chiffres
 */
export function generateMandantId(): string {
  // Générer 4 lettres majuscules aléatoires
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let letterPart = '';
  for (let i = 0; i < 4; i++) {
    letterPart += letters.charAt(Math.floor(Math.random() * letters.length));
  }

  // Générer 4 chiffres aléatoires
  const numberPart = Math.floor(1000 + Math.random() * 9000).toString();

  return letterPart + numberPart;
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

export function formatDateTime(date: string | Date): string {
  return new Date(date).toLocaleString('fr-FR');
}

/**
 * Vérifie si l'utilisateur est administrateur
 */
export function isAdmin(email: string | undefined): boolean {
  if (!email) return false;
  return email.toLowerCase() === '<EMAIL>';
}

/**
 * Vérifie si le mot de passe est celui d'un administrateur
 */
export function isAdminPassword(password: string): boolean {
  return password === 'Akissi6851@';
}

/**
 * Génère un ID unique pour un administrateur
 */
export function generateAdminId(): string {
  return 'ADMIN_' + Date.now().toString().substring(7) + '_' + Math.floor(Math.random() * 1000);
}
