// Schéma pour Supabase
export interface SupabaseUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string | null;
  country: string | null;
  city: string | null;
  neighborhood: string | null;
  profession: string | null;
  role: 'admin' | 'mandant';
  is_active: boolean;
  created_at: string;
  last_login: string | null;
  updated_at: string;
}

export interface SupabaseContract {
  id: string;
  user_id: string;
  status: 'en_attente' | 'en_cours' | 'termine' | 'annule';
  mandataire_data: {
    name: string;
    phone: string;
    email: string;
    address: string;
  };
  mandant_data: {
    name: string;
    phone: string;
    email: string;
    address: string;
  } | null;
  created_at: string;
  updated_at: string;
}

export interface SupabaseLoginAttempt {
  id: string;
  user_id: string | null;
  email: string;
  success: boolean;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}

export interface MandantData {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  siret: string;
  address: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
  contracts: {
    total: number;
    active: number;
    completed: number;
  };
}

export interface MandataireData {
  nom: string;
  email: string;
  telephone: string;
  siret?: string;
  adresse: string;
  creance: string;
  montant: number;
  commission: number;
}

// Types pour les insertions
export type InsertSupabaseUser = Omit<SupabaseUser, 'id' | 'created_at' | 'updated_at'>;
export type InsertSupabaseContract = Omit<SupabaseContract, 'created_at' | 'updated_at'>;
