import React, { useEffect, useCallback } from 'react';
import { Navigation } from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FileText, Plus, Eye, Printer, RefreshCw, AlertTriangle, ArrowRight, Edit, Trash2, Download } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { useContracts } from '@/hooks/useContracts';
import { useLocation } from 'wouter';
import { formatDate } from '@/lib/utils';
import { Contract, MandataireData, MandantData } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';

const statusColors = {
  'en_attente': 'bg-blue-100 text-blue-800',
  'accepte': 'bg-blue-100 text-blue-800',
  'rejete': 'bg-red-100 text-red-800',
  'valide': 'bg-green-100 text-green-800'
};

const statusLabels = {
  'en_attente': 'En attente',
  'accepte': 'Accepté',
  'rejete': 'Rejeté',
  'valide': 'Validé'
};

export function ContractsListPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { isAdmin } = useAdmin();
  const { 
    contracts, 
    isLoading, 
    error, 
    refreshContracts,
    createContract,
    updateContract
  } = useContracts(user?.id);
  const [, setLocation] = useLocation();

  // Fonction pour rafraîchir manuellement les contrats
  const handleRefresh = useCallback(() => {
    if (user?.id) {
      console.log('Manually refreshing contracts');
      refreshContracts();
    }
  }, [user?.id, refreshContracts]);

  useEffect(() => {
    console.log('ContractsListPage useEffect - isAuthenticated:', isAuthenticated, 'authLoading:', authLoading);

    // Don't redirect if still loading authentication state
    if (authLoading) {
      console.log('Still loading auth state in ContractsListPage, waiting...');
      return;
    }

    if (!isAuthenticated) {
      console.log('Not authenticated in ContractsListPage, redirecting to home');
      setLocation('/');
    }
  }, [isAuthenticated, authLoading, setLocation]);

  // Nous n'avons plus besoin de ce useEffect car fetchContracts est déjà appelé
  // dans le hook useContracts lorsque userId change

  const handleNewContract = () => {
    setLocation('/contract/new');
  };

  const handleViewContract = (contractId: string) => {
    setLocation(`/contract/${contractId}`);
  };

  const handlePrintContract = (contractId: string) => {
    // Simulate print functionality
    console.log('Print contract:', contractId);
    alert('Fonction d\'impression - À implémenter');
  };

  // Fonction pour gérer la suppression d'un contrat
  const handleDeleteContract = async (contractId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce contrat ? Cette action est irréversible.')) {
      try {
        // Appel API pour supprimer le contrat
        await apiRequest('DELETE', `/api/contracts/${contractId}`);
        // Recharger la liste des contrats
        refreshContracts();
      } catch (error) {
        console.error('Erreur lors de la suppression du contrat:', error);
        alert('Une erreur est survenue lors de la suppression du contrat.');
      }
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation
        onLoginClick={() => {}}
        onSignupClick={() => {}}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Mes Contrats</h1>
          <div className="flex space-x-2">
            <Button
              onClick={handleRefresh}
              variant="outline"
              disabled={isLoading}
              className="flex items-center"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
            {isAdmin && (
              <Button onClick={handleNewContract} className="bg-primary text-white hover:bg-blue-700">
                <Plus className="mr-2 h-4 w-4" />
                Nouveau contrat
              </Button>
            )}
          </div>
        </div>

        {error && (
          <Alert className="mb-6 bg-red-50 border-red-200">
            <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
            <AlertDescription className="text-red-600">
              {error} - <Button variant="link" className="p-0 h-auto text-red-600 underline" onClick={handleRefresh}>Réessayer</Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Affichage conditionnel du contenu */}
        {(() => {
          if (isLoading) {
            return (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-gray-600">Chargement des contrats...</p>
              </div>
            );
          }

          if (contracts.length === 0) {
            return (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun contrat</h3>
                <p className="text-gray-600 mb-6">
                  {isAdmin
                    ? "Commencez par créer votre premier contrat de recouvrement"
                    : "Vous n'avez pas encore de contrats en cours. Veuillez attendre qu'un mandataire crée un contrat pour vous."}
                </p>
                {isAdmin ? (
                  <Button onClick={handleNewContract} className="bg-primary text-white hover:bg-blue-700">
                    Créer un contrat
                  </Button>
                ) : (
                  <Button onClick={() => setLocation('/dashboard')} className="bg-primary text-white hover:bg-blue-700">
                    Retour au tableau de bord
                  </Button>
                )}
              </div>
            );
          }

          return (
            <div className="grid gap-4">
              {contracts.map((contract: Contract) => {
              const mandataireData = contract.mandataireData as MandataireData;
              const mandantData = contract.mandantData as MandantData;

              return (
                <div key={contract.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Contrat {contract.id.split('_')[1]}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Créé le {formatDate(contract.createdAt)}
                      </p>
                    </div>
                    <Badge className={statusColors[contract.status]}>
                      {statusLabels[contract.status]}
                    </Badge>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600">Mandataire:</p>
                      <p className="font-medium">{mandataireData?.nom || 'Non renseigné'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Mandant:</p>
                      <p className="font-medium">{mandantData?.nom || 'En attente'}</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setLocation(`/contract/${contract.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Voir
                      </Button>
                      {isAdmin && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setLocation(`/contract/${contract.id}/edit`)}
                            className="text-green-600 dark:text-green-400"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Modifier
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteContract(contract.id)}
                            className="text-red-600 dark:text-red-400"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Supprimer
                          </Button>
                        </>
                      )}
                    </div>
                    {contract.status === 'valide' && (
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Printer className="h-4 w-4 mr-1" />
                          Imprimer
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          PDF
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
            </div>
          );
        })()}
      </div>
    </div>
  );
}
