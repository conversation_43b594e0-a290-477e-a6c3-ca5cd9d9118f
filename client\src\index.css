/* Importation de la police d'écriture manuscrite pour les signatures */
@import url('https://fonts.googleapis.com/css2?family=Caveat:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 11% 98%; /* #F5F7FA */
    --foreground: 210 6% 12%; /* #1E293B */
    --card: 0 0% 100%;
    --card-foreground: 210 6% 12%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 6% 12%;
    --primary: 215 100% 60%; /* #3B82F6 */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 215 16% 25%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 215 16% 25%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 215 100% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%; /* #0F172A */
    --foreground: 213 31% 91%; /* #E2E8F0 */
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;
    --primary: 217 91% 60%; /* #3B82F6 */
    --primary-foreground: 222 84% 5%;
    --secondary: 215 28% 17%; /* #1E293B */
    --secondary-foreground: 213 31% 91%;
    --muted: 215 28% 17%;
    --muted-foreground: 217 10% 65%;
    --accent: 215 28% 17%;
    --accent-foreground: 213 31% 91%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 213 31% 91%;
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 217 91% 60%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Transitions douces pour le changement de thème */
  html {
    transition: background-color 0.3s ease-in-out;
  }

  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  }

  /* Transitions pour les éléments d'interface */
  button, a, input, textarea, select,
  .card, .dialog, .modal, .dropdown, .tooltip,
  nav, header, footer, aside, section, article,
  h1, h2, h3, h4, h5, h6, p, span, div {
    transition:
      background-color 0.3s ease-in-out,
      border-color 0.3s ease-in-out,
      color 0.3s ease-in-out,
      box-shadow 0.3s ease-in-out,
      transform 0.2s ease-in-out;
  }

  /* Animation pour le changement de thème */
  .theme-transition {
    animation: theme-fade 0.3s ease-in-out;
  }

  @keyframes theme-fade {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
  }
}