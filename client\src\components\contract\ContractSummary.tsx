import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, FileText, List, Printer, Building, User, CreditCard, Bank, CheckCircle } from 'lucide-react';
import { Contract, MandataireData, MandantData } from '@/types';
import { formatDateTime, cn } from '@/lib/utils';

interface ContractSummaryProps {
  contract: Contract;
  onPrint: () => void;
  onDownload: () => void;
  onBackToContracts: () => void;
}

export function ContractSummary({ contract, onPrint, onDownload, onBackToContracts }: Readonly<ContractSummaryProps>) {
  const mandataireData = contract.mandataireData as MandataireData;
  const mandantData = contract.mandantData as MandantData;
  const signatureDate = contract.signedAt
    ? new Date(contract.signedAt).toLocaleString('fr-FR')
    : new Date().toLocaleString('fr-FR');

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Récapitulatif et Signature</h2>

      <div className="grid md:grid-cols-2 gap-8 mb-8">
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Building className="mr-2 h-5 w-5 text-primary" />
            Mandataire (RecovExpert)
          </h3>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>Nom:</strong> {mandataireData?.nom}</p>
            <p><strong>Email:</strong> {mandataireData?.email}</p>
            <p><strong>Téléphone:</strong> {mandataireData?.telephone}</p>
            <p><strong>Adresse:</strong> {mandataireData?.adresse}</p>
            <p><strong>SIRET:</strong> {mandataireData?.siret || 'Non spécifié'}</p>
            <p><strong>Montant de la créance:</strong> {mandataireData?.montant}€</p>
            <p><strong>Commission:</strong> {mandataireData?.commission}%</p>
            <p><strong>Description de la créance:</strong> {mandataireData?.creance}</p>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <User className="mr-2 h-5 w-5 text-primary" />
            Mandant (Créancier)
          </h3>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>Nom:</strong> {mandantData?.nom}</p>
            <p><strong>Forme juridique:</strong> {mandantData?.formeJuridique || 'Non spécifié'}</p>
            <p><strong>Email:</strong> {mandantData?.email}</p>
            <p><strong>Téléphone:</strong> {mandantData?.telephone}</p>
            <p><strong>Adresse:</strong> {mandantData?.adresse}</p>
            <p><strong>SIRET/Identification fiscale:</strong> {mandantData?.siret || 'Non spécifié'}</p>
            <p><strong>Représentant:</strong> {mandantData?.representant || 'Non spécifié'}</p>
            <p><strong>Fonction:</strong> {mandantData?.fonction || 'Non spécifié'}</p>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <CreditCard className="mr-2 h-5 w-5 text-primary" />
          Coordonnées bancaires du Mandant
        </h3>
        <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
          <p><strong>Banque:</strong> {mandantData?.banque || 'Non spécifié'}</p>
          <p><strong>Numéro de compte:</strong> {mandantData?.numeroCompte || 'Non spécifié'}</p>
          <p><strong>IBAN:</strong> {mandantData?.iban || 'Non spécifié'}</p>
          <p><strong>SWIFT/BIC:</strong> {mandantData?.swift || 'Non spécifié'}</p>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-6">
        <div className="text-center">
          <p className="text-lg font-medium text-gray-900 mb-4 flex items-center justify-center">
            <CheckCircle className="text-green-500 mr-2 h-5 w-5" />
            Signature électronique
          </p>

          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            {contract.signature?.type === 'image' ? (
              <div className="mb-4">
                <p className="text-green-700 font-medium mb-3">Signature par image</p>
                <img
                  src={contract.signature.value}
                  alt="Signature"
                  className="max-h-24 mx-auto border border-green-200 rounded-lg bg-white p-2"
                />
              </div>
            ) : (
              <div className="mb-4">
                <p className="text-green-700 font-medium mb-2">Signature textuelle</p>
                <p className="text-xl font-handwriting text-green-800 p-2 bg-white rounded-lg border border-green-200">
                  {contract.signature?.value || "J'approuve"}
                </p>
              </div>
            )}

            <div className="flex items-center justify-center text-green-700">
              <FileText className="text-green-500 mr-2 h-5 w-5" />
              <p className="font-medium">Contrat signé électroniquement</p>
            </div>
            <p className="text-sm text-green-600 mt-1">Date: {signatureDate}</p>
          </div>

          <div className="flex justify-center space-x-4">
            <Button onClick={onPrint} variant="outline" className="text-gray-600 hover:text-gray-800">
              <Printer className="mr-2 h-4 w-4" />
              Imprimer
            </Button>
            <Button onClick={onDownload} className="bg-primary text-white hover:bg-blue-700">
              <Download className="mr-2 h-4 w-4" />
              Télécharger PDF
            </Button>
            <Button onClick={onBackToContracts} className="bg-green-600 text-white hover:bg-green-700">
              <List className="mr-2 h-4 w-4" />
              Mes contrats
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
