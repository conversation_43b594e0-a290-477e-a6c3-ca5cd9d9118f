import React from 'react';
import { useLocation } from 'wouter';
import { useParams } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useContract } from '@/hooks/useContract';
import { formatDate } from '@/lib/utils';
import { Building2, FileText, ArrowLeft } from 'lucide-react';

export function ContractDetailPage() {
  const [, setLocation] = useLocation();
  const [, params] = useParams();
  const contractId = params?.id;
  const { contract, isLoading, error } = useContract(contractId);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Erreur lors du chargement du contrat
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error?.message || "Le contrat n'a pas été trouvé"}
          </p>
          <Button onClick={() => setLocation('/contracts')}>
            Retour à la liste des contrats
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setLocation('/contracts')}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Détails du Contrat
        </h1>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Informations du Contrat
            </CardTitle>
            <CardDescription>
              ID: {contract.id}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Statut</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {contract.status}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Date de création</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {formatDate(contract.createdAt)}
                </dd>
              </div>
              {contract.signedAt && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Date de signature</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {formatDate(contract.signedAt)}
                  </dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        {contract.mandataireData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Informations du Mandataire
              </CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.nom}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.email}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.telephone}
                  </dd>
                </div>
                {contract.mandataireData.siret && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">SIRET</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandataireData.siret}
                    </dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.adresse}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Créance</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.creance}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Montant</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.montant} €
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Commission</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandataireData.commission}%
                  </dd>
                </div>
              </dl>
            </CardContent>
          </Card>
        )}

        {contract.mandantData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Informations du Mandant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandantData.nom}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandantData.email}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandantData.telephone}
                  </dd>
                </div>
                {contract.mandantData.siret && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">SIRET</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.siret}
                    </dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {contract.mandantData.adresse}
                  </dd>
                </div>
                {contract.mandantData.formeJuridique && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Forme juridique</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.formeJuridique}
                    </dd>
                  </div>
                )}
                {contract.mandantData.representant && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Représentant</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.representant}
                    </dd>
                  </div>
                )}
                {contract.mandantData.fonction && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Fonction</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.fonction}
                    </dd>
                  </div>
                )}
                {contract.mandantData.banque && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Banque</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.banque}
                    </dd>
                  </div>
                )}
                {contract.mandantData.numeroCompte && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro de compte</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.numeroCompte}
                    </dd>
                  </div>
                )}
                {contract.mandantData.iban && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">IBAN</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.iban}
                    </dd>
                  </div>
                )}
                {contract.mandantData.swift && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">SWIFT</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                      {contract.mandantData.swift}
                    </dd>
                  </div>
                )}
              </dl>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end gap-4">
          <Button
            variant="outline"
            onClick={() => setLocation('/contracts')}
          >
            Retour
          </Button>
          <Button
            onClick={() => setLocation(`/contract/${contract.id}/edit`)}
          >
            Modifier
          </Button>
        </div>
      </div>
    </div>
  );
} 