import { Switch, Route, useLocation } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { HomePage } from "@/pages/HomePage";
import { ContractsListPage } from "@/pages/ContractsListPage";
import { ContractPage } from "@/pages/ContractPage";
import { ContractAccessPage } from "@/pages/ContractAccessPage";
import { MandantsListPage } from "@/pages/MandantsListPage";
import { MandantDetailPage } from "@/pages/MandantDetailPage";
import { MandantEditPage } from "@/pages/MandantEditPage";
import { MandantDashboardPage } from "@/pages/MandantDashboardPage";
import { AdminPage } from "@/pages/AdminPage";
import { AdminMandantsPage } from "@/pages/AdminMandantsPage";
import { AdminDatabasePage } from "@/pages/AdminDatabasePage";
import NotFound from "@/pages/not-found";
import { useEffect } from "react";
import { useAuth } from './hooks/useAuth';
import { LoginPage } from '@/pages/LoginPage';
import { SignupPage } from '@/pages/SignupPage';
import { ContractDetailPage } from '@/pages/ContractDetailPage';
import { ContractEditPage } from '@/pages/ContractEditPage';
import { ResetPasswordPage } from '@/pages/ResetPasswordPage';

function Router() {
  const [location, setLocation] = useLocation();
  const { user } = useAuth();

  useEffect(() => {
    console.log('Current location:', location);
    // Check if we're accessing via contract link
    const urlParams = new URLSearchParams(window.location.search);
    const contractId = urlParams.get('contract');

    if (contractId && location === '/') {
      // Navigate directly to contract access page
      setLocation(`/contract-access?contract=${contractId}`);
    }
  }, [location, setLocation]);

  useEffect(() => {
    // Check if there's a contract ID in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const contractId = urlParams.get('contract');
    if (contractId) {
      window.location.href = `/contract/${contractId}`;
    }
  }, []);

  return (
    <Switch>
      <Route path="/" component={HomePage} />
      <Route path="/login" component={LoginPage} />
      <Route path="/signup" component={SignupPage} />
      <Route path="/contracts" component={ContractsListPage} />
      <Route path="/contract/:action" component={ContractPage} />
      <Route path="/contract-access" component={ContractAccessPage} />
      <Route path="/mandants" component={MandantsListPage} />
      <Route path="/mandant/:id" component={MandantDetailPage} />
      <Route path="/mandant/:id/edit" component={MandantEditPage} />
      <Route path="/mandant/new" component={MandantEditPage} />
      <Route path="/contract/:id" component={ContractDetailPage} />
      <Route path="/contract/:id/edit" component={ContractEditPage} />
      <Route path="/dashboard" component={MandantDashboardPage} />
      <Route path="/admin" component={AdminPage} />
      <Route path="/admin/mandants" component={AdminMandantsPage} />
      <Route path="/admin/database" component={AdminDatabasePage} />
      <Route path="/reset-password" component={ResetPasswordPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
