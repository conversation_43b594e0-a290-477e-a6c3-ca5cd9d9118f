{"id": "users", "name": "users", "type": "auth", "system": false, "schema": [{"id": "first_name", "name": "first_name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "last_name", "name": "last_name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "phone", "name": "phone", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "country", "name": "country", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "city", "name": "city", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "neighborhood", "name": "neighborhood", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "profession", "name": "profession", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "role", "name": "role", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["admin", "mandant"]}}, {"id": "is_active", "name": "is_active", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "last_login", "name": "last_login", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {"allowEmailAuth": true, "allowOAuth2Auth": false, "allowUsernameAuth": false, "exceptEmailDomains": null, "manageRule": null, "minPasswordLength": 6, "onlyEmailDomains": null, "requireEmail": true}}