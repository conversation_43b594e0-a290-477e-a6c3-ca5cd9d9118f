import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { MandantData } from '@/types';
import { ArrowLeft, ArrowRight, Building, User, CreditCard, Bank } from 'lucide-react';

interface MandantFormProps {
  onSubmit: (data: MandantData) => void;
  onBack: () => void;
  initialData?: MandantData;
}

export function MandantForm({ onSubmit, onBack, initialData }: MandantFormProps) {
  const [formData, setFormData] = useState<MandantData>(
    initialData || {
      nom: '',
      email: '',
      telephone: '',
      siret: '',
      adresse: '',
      formeJuridique: '',
      representant: '',
      fonction: '',
      banque: '',
      numeroCompte: '',
      iban: '',
      swift: '',
    }
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Informations du Mandant (Créancier)</h2>
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Section 1: Informations générales */}
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Building className="mr-2 h-5 w-5 text-primary" />
            Informations générales
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="nom" className="block text-sm font-medium text-gray-700 mb-2">
                Nom complet / Dénomination sociale
              </Label>
              <Input
                type="text"
                name="nom"
                value={formData.nom}
                onChange={handleInputChange}
                required
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="formeJuridique" className="block text-sm font-medium text-gray-700 mb-2">
                Forme juridique
              </Label>
              <Input
                type="text"
                name="formeJuridique"
                value={formData.formeJuridique}
                onChange={handleInputChange}
                placeholder="SARL, SAS, Auto-entrepreneur, etc."
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="telephone" className="block text-sm font-medium text-gray-700 mb-2">
                Téléphone
              </Label>
              <Input
                type="tel"
                name="telephone"
                value={formData.telephone}
                onChange={handleInputChange}
                required
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="siret" className="block text-sm font-medium text-gray-700 mb-2">
                Numéro d'identification fiscale / SIRET
              </Label>
              <Input
                type="text"
                name="siret"
                value={formData.siret}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="adresse" className="block text-sm font-medium text-gray-700 mb-2">
                Adresse complète
              </Label>
              <Textarea
                name="adresse"
                value={formData.adresse}
                onChange={handleInputChange}
                required
                rows={2}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Section 2: Représentant légal */}
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <User className="mr-2 h-5 w-5 text-primary" />
            Représentant légal
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="representant" className="block text-sm font-medium text-gray-700 mb-2">
                Nom et prénom du représentant légal
              </Label>
              <Input
                type="text"
                name="representant"
                value={formData.representant}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="fonction" className="block text-sm font-medium text-gray-700 mb-2">
                Fonction du représentant
              </Label>
              <Input
                type="text"
                name="fonction"
                value={formData.fonction}
                onChange={handleInputChange}
                placeholder="Gérant, Directeur, etc."
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Section 3: Coordonnées bancaires */}
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CreditCard className="mr-2 h-5 w-5 text-primary" />
            Coordonnées bancaires
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="banque" className="block text-sm font-medium text-gray-700 mb-2">
                Nom de la banque
              </Label>
              <Input
                type="text"
                name="banque"
                value={formData.banque}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="numeroCompte" className="block text-sm font-medium text-gray-700 mb-2">
                Numéro de compte
              </Label>
              <Input
                type="text"
                name="numeroCompte"
                value={formData.numeroCompte}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="iban" className="block text-sm font-medium text-gray-700 mb-2">
                Code IBAN
              </Label>
              <Input
                type="text"
                name="iban"
                value={formData.iban}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="swift" className="block text-sm font-medium text-gray-700 mb-2">
                Code SWIFT/BIC
              </Label>
              <Input
                type="text"
                name="swift"
                value={formData.swift}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <Button type="button" variant="ghost" onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
          <Button type="submit" className="bg-primary text-white hover:bg-blue-700 px-8 py-3">
            Continuer vers les articles
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}
