eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
eruda.js:2 Error fetching contracts: TypeError: Failed to fetch
    at window.fetch (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218642)
    at fetchContracts (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/hooks/useContracts.ts:10:30)
    at 3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/src/pages/ContractsListPage.tsx:57:7
    at commitHookEffectListMount (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:16915:34)
    at commitPassiveMountOnFiber (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18156:19)
    at commitPassiveMountEffects_complete (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18129:17)
    at commitPassiveMountEffects_begin (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18119:15)
    at commitPassiveMountEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:18109:11)
    at flushPassiveEffectsImpl (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19490:11)
    at flushPassiveEffects (3374f3d0-fa77-4309-b167-5dd83611a107-00-10ft2zk2agqer.janeway.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=2cc59531:19447:22)
Mt.forEach.n.<computed> @ Console.js:61
fetchContracts @ useContracts.ts:19
await in fetchContracts
(anonymous) @ ContractsListPage.tsx:48
commitHookEffectListMount @ chunk-WERSD76P.js?v=2cc59531:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=2cc59531:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=2cc59531:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=2cc59531:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=2cc59531:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=2cc59531:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=2cc59531:19447
(anonymous) @ chunk-WERSD76P.js?v=2cc59531:19328
workLoop @ chunk-WERSD76P.js?v=2cc59531:197
flushWork @ chunk-WERSD76P.js?v=2cc59531:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=2cc59531:384
4899-f67c9c7961ca8781.js:1 WebSocket connection to 'wss://eval.janeway.platform.replit.com/wsv2/v2.public.Q2dZSTFmaSt3UVlTQmdqMGxML0JCaUlIYW1GdVpYZGhlVEFBT3Q4QkNpUXpNemMwWmpOa01DMW1ZVGMzTFRRek1Ea3RZakUyTnkwMVpHUTRNell4TVdFeE1EY1NBMjVwZUJvWmNtVndiR2wwTFhKbGNHd3RabWxzWlhNdGFtRnVaWGRoZVNJUVYyaGhkSE5oY0hCU1pXTnZkbVZ5ZVNvUGEyOW1abWxyYjNWaGEyOTFZV3hoUWxjS0duSmxjR3hwZEMxeVpYQnNMV0pzYjJOcmN5MXFZVzVsZDJGNUVoaHlaWEJzYVhRdGNtVndiQzF0WlhSaExXcGhibVYzWVhrYUgzSmxjR3hwZEMxdFlYSm5ZWEpwYm1VdFlteHZZMnR6TFdwaGJtVjNZWGxLQndpVnVzRVVFQUZRQUZvT2FXNTBaVzUwWDNOMGRXUmxiblI0QUZJa0NBRVFnSUNBZ0FnWkFBQUFBQUFBRUVBaEFBQUFBQUFBRUVBb2dJQ0FnQkF3QWpnQVlBQnFGZ2lWdXNFVUVnOXJiMlptYVd0dmRXRnJiM1ZoYkdGeUdYVnpaUzFzWVhWdVkyaGtZWEpyYkhrdFpHbHlaV04wYkhtQ0FRSXFBSm9CQ2dvSU1DNHdMakV6TXpHZ0FRQT2LqgZnN-tCVQGmOo4v6AYXM2CiD4oKi2liIVtqQGS3vxT2ikqBMkbSQbYLKYFlr85UvS16XjLkWp2xnqlka8YN.Q2dad2NtOWtPak1pQ25KbGNHeHBkQzVqYjIwPQ' failed: WebSocket is closed before the connection is established.
cleanupSocket @ 4899-f67c9c7961ca8781.js:1
m @ 4899-f67c9c7961ca8781.js:1
(anonymous) @ 4899-f67c9c7961ca8781.js:1
r @ _app-5f527e502df8030b.js:38
setTimeout
(anonymous) @ _app-5f527e502df8030b.js:38
g @ 4899-f67c9c7961ca8781.js:1
connect @ 4899-f67c9c7961ca8781.js:1
handleClose @ 4899-f67c9c7961ca8781.js:1
t @ 4899-f67c9c7961ca8781.js:1
