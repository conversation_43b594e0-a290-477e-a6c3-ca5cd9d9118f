import PocketBase from 'pocketbase';

// Configuration PocketBase
const PB_URL = import.meta.env.VITE_POCKETBASE_URL || 'http://localhost:8090';

// Instance PocketBase
export const pb = new PocketBase(PB_URL);

// Types pour l'authentification
export interface AuthUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'mandant';
  phone?: string;
  country?: string;
  city?: string;
  neighborhood?: string;
  profession?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

// Types pour les contrats
export interface Contract {
  id: string;
  title: string;
  description: string;
  amount: number;
  currency: string;
  debtor_name: string;
  debtor_email: string;
  debtor_phone?: string;
  creditor_id: string;
  status: 'draft' | 'sent' | 'signed' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  signed_at?: string;
  signature_data?: string;
}

// Fonctions utilitaires
export const isAuthenticated = () => {
  return pb.authStore.isValid;
};

export const getCurrentUser = (): AuthUser | null => {
  if (!pb.authStore.isValid) return null;
  return pb.authStore.model as AuthUser;
};

export const isAdmin = (): boolean => {
  const user = getCurrentUser();
  return user?.role === 'admin';
};

// Fonctions d'authentification
export const login = async (email: string, password: string): Promise<AuthUser> => {
  const authData = await pb.collection('users').authWithPassword(email, password);
  return authData.record as AuthUser;
};

export const logout = () => {
  pb.authStore.clear();
};

export const signup = async (userData: {
  email: string;
  password: string;
  passwordConfirm: string;
  first_name: string;
  last_name: string;
  phone?: string;
  country?: string;
  city?: string;
  neighborhood?: string;
  profession?: string;
}): Promise<AuthUser> => {
  const record = await pb.collection('users').create({
    ...userData,
    role: 'mandant',
    is_active: true,
  });
  return record as AuthUser;
};

// Fonctions CRUD pour les contrats
export const getContracts = async (userId?: string): Promise<Contract[]> => {
  let filter = '';
  if (userId) {
    filter = `creditor_id = "${userId}"`;
  }
  
  const records = await pb.collection('contracts').getList(1, 50, {
    filter,
    sort: '-created_at',
  });
  
  return records.items as Contract[];
};

export const createContract = async (contractData: Partial<Contract>): Promise<Contract> => {
  const record = await pb.collection('contracts').create(contractData);
  return record as Contract;
};

export const updateContract = async (id: string, data: Partial<Contract>): Promise<Contract> => {
  const record = await pb.collection('contracts').update(id, data);
  return record as Contract;
};

export const deleteContract = async (id: string): Promise<void> => {
  await pb.collection('contracts').delete(id);
};

// Fonctions pour les utilisateurs (admin)
export const getUsers = async (): Promise<AuthUser[]> => {
  const records = await pb.collection('users').getList(1, 50, {
    sort: '-created_at',
  });
  return records.items as AuthUser[];
};

export const updateUser = async (id: string, data: Partial<AuthUser>): Promise<AuthUser> => {
  const record = await pb.collection('users').update(id, data);
  return record as AuthUser;
};

// Écouter les changements d'authentification
pb.authStore.onChange((token, model) => {
  console.log('🔐 Auth state changed:', { 
    isValid: pb.authStore.isValid, 
    user: model?.email 
  });
});

console.log('🚀 PocketBase initialized:', PB_URL);
