import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/Navigation';
import { LoginModal } from '@/components/LoginModal';
import { SignupModal } from '@/components/SignupModal';
import { Button } from '@/components/ui/button';
import { FileText, Check, X } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useContracts } from '@/hooks/useContracts';
import { Contract, MandataireData } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';

export function ContractAccessPage() {
  const [, setLocation] = useLocation();
  const { isAuthenticated } = useAuth();
  const { 
    getContract, 
    updateContract,
    isLoading: isContractLoading
  } = useContracts();

  const [contract, setContract] = useState<Contract | null>(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showSignupModal, setShowSignupModal] = useState(false);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const contractId = urlParams.get('contract');

    if (contractId) {
      // Toujours charger le contrat, qu'on soit authentifié ou non
      loadContract(contractId);

      // Si l'utilisateur n'est pas authentifié, afficher la modal de connexion
      if (!isAuthenticated) {
        console.log('User not authenticated, showing login modal');
        setShowLoginModal(true);
      }
    } else {
      // Pas de contractId dans l'URL, rediriger vers la page d'accueil
      setLocation('/');
    }
  }, [isAuthenticated]);

  const loadContract = async (contractId: string) => {
    try {
      const existingContract = await getContract(contractId);
      if (existingContract) {
        setContract(existingContract);

        // Si l'utilisateur est authentifié, on peut rediriger selon le statut
        if (isAuthenticated) {
          // Si le contrat est déjà traité (validé ou accepté), rediriger vers la page de détail
          if (existingContract.status === 'valide' ||
              existingContract.status === 'accepte' ||
              existingContract.mandantData) {
            setLocation(`/contract/${contractId}`);
          }
          // Sinon, on reste sur cette page pour que l'utilisateur puisse accepter/rejeter
        }
      } else {
        alert('Contrat non trouvé');
        setLocation('/');
      }
    } catch (error) {
      console.error('Error loading contract:', error);
      setLocation('/');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptContract = () => {
    if (!contract) return;

    if (isAuthenticated) {
      // Rediriger vers la page du contrat avec l'étape du formulaire Mandant (étape 3)
      setLocation(`/contract/${contract.id}?step=3`);
    } else {
      // Si non authentifié, afficher la modal de connexion
      setShowLoginModal(true);
    }
  };

  const handleRejectContract = async () => {
    if (!contract) return;

    try {
      await updateContract(contract.id, { status: 'rejete' });
      alert('Contrat rejeté');
      setLocation('/');
    } catch (error) {
      console.error('Error rejecting contract:', error);
    }
  };

  const handleAuthSuccess = () => {
    if (contract) {
      // Après authentification réussie, rediriger vers la page du contrat avec l'étape du formulaire Mandant
      setLocation(`/contract/${contract.id}?step=3`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation onLoginClick={() => {}} onSignupClick={() => {}} />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!contract) {
    return null;
  }

  const mandataireData = contract.mandataireData as MandataireData;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation
        onLoginClick={() => setShowLoginModal(true)}
        onSignupClick={() => setShowSignupModal(true)}
      />

      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-sm p-8 text-center">
          <div className="mb-6">
            <div className="bg-primary/10 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText className="h-8 w-8 text-primary" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Contrat de Recouvrement</h2>
            <p className="text-gray-600">RecovExpert vous a envoyé un contrat, voulez-vous le signer ?</p>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <h3 className="font-semibold text-gray-900 mb-2">Détails du contrat</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>Créancier:</strong> {mandataireData?.nom}</p>
              <p><strong>Montant:</strong> {mandataireData?.montant}€</p>
              <p><strong>Description:</strong> {mandataireData?.creance}</p>
            </div>
          </div>

          <p className="text-sm text-gray-600 mb-6">
            En acceptant, vous vous engagez à compléter vos informations et à signer le contrat.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={handleAcceptContract}
              className="bg-green-600 text-white hover:bg-green-700 px-8 py-3"
            >
              <Check className="mr-2 h-4 w-4" />
              Accepter
            </Button>
            <Button
              onClick={handleRejectContract}
              variant="destructive"
              className="px-8 py-3"
            >
              <X className="mr-2 h-4 w-4" />
              Rejeter
            </Button>
          </div>
        </div>
      </div>

      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onSwitchToSignup={() => {
          setShowLoginModal(false);
          setShowSignupModal(true);
        }}
        onSuccess={handleAuthSuccess}
      />

      <SignupModal
        isOpen={showSignupModal}
        onClose={() => setShowSignupModal(false)}
        onSwitchToLogin={() => {
          setShowSignupModal(false);
          setShowLoginModal(true);
        }}
        onSuccess={handleAuthSuccess}
      />
    </div>
  );
}
