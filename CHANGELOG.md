# Changelog

## [Améliorations récentes] - 2025-05-24

### Refactorisation majeure du hook useContracts

- Migration complète vers React Query pour une meilleure gestion des requêtes
- Ajout de typage fort pour les erreurs (ApiError)
- Optimisation du cache avec staleTime et invalidations
- Simplification de la gestion des états de chargement et d'erreur
- Ajout de mutations optimistes pour une meilleure expérience utilisateur

### Améliorations des pages

- **ContractsListPage** :
  - Utilisation du nouveau hook useContracts
  - Meilleure gestion des erreurs
  - Optimisation des re-renders

- **ContractPage** :
  - Simplification de la gestion d'état
  - Meilleure intégration avec React Query
  - Amélioration de la gestion des étapes

- **ContractAccessPage** :
  - Refactorisation pour utiliser le nouveau hook
  - Meilleure gestion des flux d'authentification

### Autres améliorations

- Standardisation des appels API via apiRequest
- Meilleure gestion des dépendances
- Préparation pour les tests unitaires

### Prochaines étapes

- Ajouter des tests unitaires pour le hook useContracts
- Documenter les bonnes pratiques d'utilisation
- Optimiser les performances des listes
