import React from 'react';
import { FileText, Moon, Sun, User, LogOut, Users, Shield, List } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useTheme } from '@/hooks/useTheme';
import { useAdmin } from '@/hooks/useAdmin';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useLocation } from 'wouter';

interface NavigationProps {
  onLoginClick: () => void;
  onSignupClick: () => void;
}

export function Navigation({ onLoginClick, onSignupClick }: Readonly<NavigationProps>) {
  const { user, isAuthenticated, logout } = useAuth();
  const { isDark, isTransitioning, toggleTheme } = useTheme();
  const { isAdmin } = useAdmin();
  const [, setLocation] = useLocation();

  // Fonction pour déterminer la page d'accueil en fonction du statut de l'utilisateur
  const getHomePage = () => {
    if (!isAuthenticated) return '/';
    if (isAdmin) return '/admin';
    return '/dashboard';
  };

  return (
    <nav className={cn(
      "bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-6 py-4",
      isTransitioning && "theme-transition"
    )}>
      <div className="flex items-center justify-between max-w-6xl mx-auto">
        <div className="flex items-center space-x-2">
          <Button
            variant="link"
            className="bg-primary text-white p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-center justify-center"
            onClick={() => setLocation(getHomePage())}
          >
            <FileText className="h-5 w-5" />
          </Button>
          <Button
            variant="link"
            className="text-xl font-bold text-gray-900 dark:text-white p-0"
            onClick={() => setLocation(getHomePage())}
          >
            RecovExpert
          </Button>
        </div>

        {/* Navigation principale */}
        {isAuthenticated && (
          <div className="hidden md:flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => setLocation('/contracts')}
              className={cn(
                "text-gray-700 dark:text-gray-300",
                isAdmin ? "hover:bg-blue-50 dark:hover:bg-blue-900/20" : "opacity-50 cursor-not-allowed"
              )}
              disabled={!isAdmin}
            >
              <List className="mr-2 h-4 w-4" />
              Contrats
            </Button>

            <Button
              variant="ghost"
              onClick={() => setLocation('/mandants')}
              className={cn(
                "text-gray-700 dark:text-gray-300",
                isAdmin ? "hover:bg-blue-50 dark:hover:bg-blue-900/20" : "opacity-50 cursor-not-allowed"
              )}
              disabled={!isAdmin}
            >
              <Users className="mr-2 h-4 w-4" />
              Mandants
            </Button>

            {isAdmin && (
              <Button
                variant="ghost"
                onClick={() => setLocation('/admin')}
                className="text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                <Shield className="mr-2 h-4 w-4" />
                Administration
              </Button>
            )}
          </div>
        )}

        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className={cn(
              "p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",
              "transition-transform hover:scale-110 active:scale-95"
            )}
            aria-label={isDark ? "Passer au mode clair" : "Passer au mode sombre"}
          >
            {isDark ? (
              <Sun className="h-5 w-5 text-yellow-400" />
            ) : (
              <Moon className="h-5 w-5 text-blue-600" />
            )}
          </Button>

          {isAuthenticated ? (
            <>
              <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">
                <User className={cn(
                  "h-4 w-4",
                  isAdmin
                    ? "text-green-600 dark:text-green-400"
                    : "text-gray-600 dark:text-gray-300"
                )} />
                <span className={cn(
                  "text-sm font-medium",
                  isAdmin
                    ? "text-green-700 dark:text-green-400"
                    : "text-gray-700 dark:text-gray-300"
                )}>
                  {user?.name}
                  {isAdmin && <span className="ml-1 text-xs bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 px-1.5 py-0.5 rounded-full">Admin</span>}
                </span>
              </div>
              <Button
                variant="outline"
                onClick={logout}
                className="flex items-center space-x-1 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400"
              >
                <LogOut className="h-4 w-4" />
                <span>Déconnexion</span>
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="ghost"
                onClick={onLoginClick}
                className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                Se connecter
              </Button>
              <Button
                onClick={onSignupClick}
                className="bg-primary hover:bg-primary/90 text-white shadow-md hover:shadow-lg transition-shadow"
              >
                S'inscrire
              </Button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
}